'use client';

import React from 'react';
import Image from 'next/image';

const VIDEO_ID = 'FEsl2xNDrmg';

interface Thumbnail {
  id: string;
  title: string;
  url: string;
  summary: string;
}

export const dummyThumbnails: Thumbnail[] = [
  {
    id: '1',
    title: `<PERSON> sir vs <PERSON><PERSON><PERSON> I Youtuber's Comparison I #khan #khansir #vikasdivyakirtisir #upsc`,
    url: 'https://img.youtube.com/vi/6kJ7li2OdlM/maxresdefault.jpg',
    summary: `# **Highlights**
  📍 <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> teach concepts in Patna, while <PERSON><PERSON><PERSON> and <PERSON> prepare students for UPSC in Delhi.
  
  🏫 <PERSON><PERSON><PERSON> started teaching for the UPSC around 23 years ago, whereas <PERSON> began 4-6 years ago.

  🎥 Both <PERSON><PERSON><PERSON> and <PERSON> have their own YouTube channels where they offer free learning materials.
  
  📱 Each teacher also has their own mobile applications for educational purposes.

  🌟 Both teachers are humble and down-to-earth, with no inclination towards showing off.
  
  📚 While <PERSON><PERSON><PERSON> is an expert in his domain, <PERSON>'s course is priced at just ₹150 per month, making it accessible even to the poorer students.
  
  🧑 ‍💻 They make learning fun and engaging, with their content encompassing half Hindi and half Bhojpuri, rather than dull and boring.
  
  🏅 As a result, approximately 1.5 million students study online through <PERSON>'s platform.
  
  📈 <PERSON>ikas Sir himself cleared the UPSC on his first attempt and served as an IAS officer, boasting 23 years of teaching experience.
  
  🎤 His speeches are so engaging that students lose track of time while listening.
  
  🌍 The combined experience and teaching methodologies of both Vikas Sir and Khan Sir make them exceptional educators.
  
  # **Key Insights**
  👨 ‍🏫 **Diverse Expertise in Teaching:** Vikas Sir and Khan Sir bring varied expertise and backgrounds to their teaching. Vikas Sir's extensive experience and background as an IAS officer contrast with Khan Sir's more recent but equally impactful methods. This diversity enriches the learning experience for students.
  
  💡 **Accessibility of Education:** Khan Sir’s affordable course pricing at ₹150 per month opens opportunities for economically disadvantaged students to prepare for competitive exams like the UPSC, highlighting an essential step towards educational equity.
  
  🎯 **Engaging Learning Methods:** The teachers' initiative to make learning enjoyable and less monotonous by incorporating regional languages (Hindi and Bhojpuri) demonstrates a deep understanding of student psychology and the importance of cultural context in education.
  
  🌐 **Impact of Online Education:** With approximately 1.5 million students engaged online, the reach and impact of these educators underscore the transformative power of digital platforms in democratizing education.
  
  🔄 **Blending Traditional and Modern Methods:** While both teachers utilize YouTube and mobile apps, their non-showy, down-to-earth approach reflects traditional values of humility and dedication, harmoniously blended with modern, tech-enabled teaching methods.
  
  # **Summary**
  
  Divya and Kriti teach concepts in Patna while Vikas Sir and Khan Sir prepare students for the UPSC, mainly based in Delhi. Vikas Sir, with 23 years of experience, started teaching for UPSC 23 years ago, whereas Khan Sir began around 4-6 years ago. Both have their own YouTube channels and mobile applications where they provide free educational resources. Notably humble and down-to-earth, these educators avoid any shows of ostentation. Khan Sir’s courses are priced at just ₹150 per month, making them accessible to the economically disadvantaged, and his half Hindi, half Bhojpuri content transforms learning from a boring task to an engaging activity, drawing about 1.5 million online students. Vikas Sir, a former IAS officer, cleared the UPSC on his first attempt and brings 23 years of teaching experience. Renowned for his engaging lectures, students often lose track of time while listening to him. Together, Vikas Sir and Khan Sir’s extensive experience, accessible pricing, engaging methods, and use of digital platforms significantly impact students preparing for the UPSC, making them exceptional educators in their field.`
  },
  {
    id: '2',
    url: `https://i.ytimg.com/vi/bqt7qh5oq38/mqdefault.jpg`,
    title: 'How to Build Your Weekly Workout Program | Jeff Cavaliere & Dr. Andrew Huberman',
    summary: `# **Highlights**
🏋 ️‍♂️ One mantra emphasized: "If you want to look like an athlete, train like an athlete."

🏃 ‍♂️ Discussion on balance between resistance training and cardio for overall health, aesthetics, and athleticism. Suggested ratio: 60/40, favoring strength training.

📅 Recommended schedule: Strength training on Monday, Wednesday, Friday, and conditioning on Tuesday, Thursday.

⏱ Duration suggestion: Strength training for no more than 60 minutes, conditioning for about 30-45 minutes.

🧊 Key insight: Short, intense workouts are more effective and easier to recover from than longer sessions.

🧓 Age consideration: Older individuals may need longer warm-ups, and workouts should be adjusted to prevent aches from longer durations.

🔥 Quote: "You can either train long or you can train hard, but you can't do both."

🏋 ️ Special mention: Includes personal experiences and adjustments in training routine as age progresses.

  
# **Key Insights**

💪 **Balanced Approach**: A 60/40 split between strength training and cardio can provide optimal benefits for maintaining muscle mass and cardiovascular health.

⏳ **Efficiency Over Length**: Short, intense workouts (under an hour) are often more beneficial and sustainable than longer sessions, especially for those with busy schedules.

🔄 **Adaptation with Age**: Training routines need to be adjusted over time, with more emphasis on warm-ups and careful consideration of workout duration to prevent overuse injuries.

🔥 **Intensity Matters**: The philosophy of "train hard or train long" underscores the importance of workout intensity over duration for better physical results.

🕰 ️ **Customized Duration**: Typical workouts should last about 60 minutes for strength training and 30-45 minutes for cardio to ensure effective results without overtraining.

  
# **Summary**

The conversation on training approaches highlights the importance of a balanced workout regime consisting of both strength training and cardiovascular exercises. The recommended ratio of 60% strength training to 40% cardio helps maintain muscle mass and cardiovascular health. Optimal scheduling involves strength training thrice a week, with conditioning twice a week.

Key insights draw attention to the efficiency of shorter, high-intensity workouts. Individuals, especially as they age, should prioritize proper warm-ups and manage workout durations to avoid overuse injuries. The experts emphasize that training hard for a shorter period is more effective than longer sessions. Overall, the dialogue underscores the need for personalized, adaptable workout routines that evolve with age and fitness goals.
`
  }
];

interface VideoThumbnailsProps {
  onThumbnailClick?: (data: {
    title: string;
    summary: string;
    thumbnail: string;
    videoId: string;
  }) => void;
}

export default function VideoThumbnails({ onThumbnailClick }: VideoThumbnailsProps) {
  const handleClick = (thumbnail: Thumbnail) => {
    if (onThumbnailClick) {
      onThumbnailClick({
        title: thumbnail.title,
        summary: thumbnail.summary,
        thumbnail: thumbnail.url,
        videoId: VIDEO_ID
      });
    }
  };

  return (
    <div>
      <div className="grid grid-cols-2 gap-3">
        {dummyThumbnails.map((thumbnail: Thumbnail) => (
          <div 
            key={thumbnail.id} 
            onClick={() => handleClick(thumbnail)}
            className="group relative rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700 hover:border-primary transition-colors cursor-pointer"
          >
            <div className="relative aspect-video">
              <Image
                src={thumbnail.url}
                alt={thumbnail.title}
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              />
              {/* <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity" /> */}
              {/* <div className="absolute bottom-0 left-0 right-0 p-2">
                <p className="text-xs font-medium text-white line-clamp-2">
                  {thumbnail.title}
                </p>
              </div> */}
              {/* <div className="absolute top-1 right-1 bg-black/80 text-white text-[10px] px-1.5 py-0.5 rounded">
                {thumbnail.duration}
              </div> */}
              <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                <div className="w-8 h-8 rounded-full bg-primary/90 flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="white">
                    <polygon points="8 5 19 12 8 19 8 5" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
