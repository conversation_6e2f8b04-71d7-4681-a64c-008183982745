"use client";

import { LanguagesIcon } from "lucide-react";
import Select from "../ui/SelectPlain";
import { useFormContext } from "./FormContext";
import { OutputLanguage } from "@/app/actions";

export default function LanguageSelector() {
  const { language, setLanguage } = useFormContext();

  const languageOptions = [
    { value: "english", label: "English" },
    { value: "hindi", label: "Hindi" },
    { value: "spanish", label: "Spanish" },
    { value: "french", label: "French" },
    { value: "german", label: "German" },
    { value: "chinese", label: "Chinese" },
    { value: "japanese", label: "Japanese" },
  ];

  return (
      <div className="flex flex-wrap items-center gap-2 w-full sm:w-auto">
        <div className="flex items-center gap-1">
          <LanguagesIcon className="w-3.5 h-3.5 flex-shrink-0" />
        </div>
        <Select
          id="language"
          className="text-xs bg-gray-700 text-white rounded-md px-2 py-1 border border-gray-600 focus:border-purple-500 focus:ring-1 focus:ring-purple-500 outline-none"
          value={language}
          onChange={(value) => setLanguage(value as OutputLanguage)}
          options={languageOptions}
          title="Select the language for your summary output"
        />
      </div>
  );
}
