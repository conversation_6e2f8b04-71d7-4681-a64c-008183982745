'use client';

import React, { useState } from 'react';
import { useFormContext } from './FormContext';
import SummaryDisplay from './SummaryDisplay';
import EmptySummaryState from './EmptySummaryState';
import LoadingSummaryState from './LoadingSummaryState';
import MarkdownRenderer from '../MarkdownRenderer';
import { Button } from '@/components/ui/button';
import LanguageSelector from './LanguageSelector';
import StyleSelector from './StyleSelector';
import SizeSelector from './SizeSelector';

export default function TabsContainer() {
  const { post, transcript, loading } = useFormContext();
  const [activeTab, setActiveTab] = useState<'summary' | 'transcript'>('summary');

  // Auto-switch to transcript tab if transcript is available but summary is not
  React.useEffect(() => {
    if (transcript && !post) {
      setActiveTab('transcript');
    } else if (post) {
      setActiveTab('summary');
    }
  }, [transcript, post]);

  return (
    <div className="space-y-4">
      {/* Tabs - Modern design with matching border radius */}
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
          <button
            onClick={() => setActiveTab('summary')}
            className={`px-4 py-2 rounded-lg flex items-center space-x-2 text-sm font-medium transition-colors ${
              activeTab === 'summary'
                ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200/50 dark:hover:bg-gray-700/50'
            }`}
            aria-current={activeTab === 'summary' ? 'page' : undefined}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="18"
              height="18"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="flex-shrink-0"
            >
              <path d="M21 2l-2 2m-7.61 7.61a5.5 5.5 0 1 1-7.778 7.778 5.5 5.5 0 0 1 7.777-7.777zm0 0L15.5 7.5m0 0l3 3L22 7l-3-3m-3.5 3.5L19 4" />
            </svg>
            <span>Summary</span>
          </button>

          <button
            className="px-4 py-2 rounded-lg flex items-center space-x-2 text-sm font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200/50 dark:hover:bg-gray-700/50 transition-colors"
          >
            <svg
              width="18"
              height="18"
              viewBox="0 0 22 22"
              fill="currentColor"
              xmlns="http://www.w3.org/2000/svg"
              className="flex-shrink-0"
            >
              <path d="M7.5 14C6.28125 14 5.1875 13.75 4.21875 13.2812C3.96875 13.4062 3.71875 13.5 3.4375 13.625C2.84375 13.8438 2.15625 14 1.5 14C1.28125 14 1.09375 13.875 1 13.6562C0.9375 13.4688 1.03125 13.25 1.1875 13.125V13.0938H1.21875C1.25 13.0625 1.3125 13 1.375 12.9688C1.46875 12.875 1.625 12.7188 1.78125 12.5312C1.9375 12.3125 2.125 12.0312 2.21875 11.75C1.4375 10.8438 1 9.71875 1 8.5C1 5.46875 3.90625 3 7.5 3C11.0625 3 14 5.46875 14 8.5C14 11.5625 11.0625 14 7.5 14ZM15 8.5H14.9688C14.9688 8.34375 14.9688 8.1875 14.9688 8.03125C18.3438 8.21875 20.9688 10.625 20.9688 13.5C20.9688 14.7188 20.5312 15.8438 19.75 16.75C19.8438 17.0312 20.0312 17.2812 20.1875 17.5C20.3438 17.7188 20.5 17.8438 20.5938 17.9688C20.6562 18 20.7188 18.0625 20.75 18.0625C20.75 18.0938 20.7812 18.0938 20.7812 18.0938C20.9688 18.25 21.0312 18.4688 20.9688 18.6562C20.9062 18.875 20.6875 19 20.5 19C19.8125 19 19.125 18.8438 18.5312 18.625C18.25 18.5 18 18.4062 17.75 18.2812C16.7812 18.75 15.6875 19 14.5 19C11.5 19 8.96875 17.3125 8.21875 14.9688C11.875 14.6562 15 12.0312 15 8.5Z" />
            </svg>
            <span>Chat</span>
          </button>

          <button
            onClick={() => transcript && setActiveTab('transcript')}
            className={`px-4 py-2 rounded-lg flex items-center space-x-2 text-sm font-medium transition-colors ${
              activeTab === 'transcript'
                ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                : transcript
                  ? 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200/50 dark:hover:bg-gray-700/50'
                  : 'text-gray-300 dark:text-gray-600 cursor-not-allowed'
            }`}
            aria-current={activeTab === 'transcript' ? 'page' : undefined}
            disabled={!transcript}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="14"
              viewBox="0 0 16 14"
              fill="currentColor"
              className="flex-shrink-0"
            >
              <path d="M1.25 0.5H2.75C3.15625 0.5 3.5 0.84375 3.5 1.25V2.75C3.5 3.1875 3.15625 3.5 2.75 3.5H1.25C0.8125 3.5 0.5 3.1875 0.5 2.75V1.25C0.5 0.84375 0.8125 0.5 1.25 0.5ZM6 1H15C15.5312 1 16 1.46875 16 2C16 2.5625 15.5312 3 15 3H6C5.4375 3 5 2.5625 5 2C5 1.46875 5.4375 1 6 1ZM6 6H15C15.5312 6 16 6.46875 16 7C16 7.5625 15.5312 8 15 8H6C5.4375 8 5 7.5625 5 7C5 6.46875 5.4375 6 6 6ZM6 11H15C15.5312 11 16 11.4688 16 12C16 12.5625 15.5312 13 15 13H6C5.4375 13 5 12.5625 5 12C5 11.4688 5.4375 11 6 11ZM0.5 6.25C0.5 5.84375 0.8125 5.5 1.25 5.5H2.75C3.15625 5.5 3.5 5.84375 3.5 6.25V7.75C3.5 8.1875 3.15625 8.5 2.75 8.5H1.25C0.8125 8.5 0.5 8.1875 0.5 7.75V6.25ZM1.25 10.5H2.75C3.15625 10.5 3.5 10.8438 3.5 11.25V12.75C3.5 13.1875 3.15625 13.5 2.75 13.5H1.25C0.8125 13.5 0.5 13.1875 0.5 12.75V11.25C0.5 10.8438 0.8125 10.5 1.25 10.5Z" />
            </svg>
            <span>Transcript</span>
          </button>
        </div>
        
        <div className="flex items-center space-x-2">
          <button className="p-2 rounded-lg text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200/50 dark:hover:bg-gray-700/50 transition-colors">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="18"
              height="18"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
            </svg>
          </button>
          
          <button className="p-2 rounded-lg text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200/50 dark:hover:bg-gray-700/50 transition-colors">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="18"
              height="18"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
              <circle cx="8.5" cy="8.5" r="1.5" />
              <polyline points="21 15 16 10 5 21" />
            </svg>
          </button>
        </div>
      </div>

      {/* Tab content */}
      <div className="min-h-[400px]">
        {/* Summary Tab Content */}
        <div className={`space-y-4 transition-opacity duration-200 ${activeTab === 'summary' ? 'block' : 'hidden'}`}>
          {/* Always show filters in the summary tab */}
          <div className="w-full">
            <div className="flex flex-wrap gap-2 justify-between mb-4">
              <div className="flex items-center flex-1 min-w-[150px]">
                <div className="flex-grow">
                  <LanguageSelector />
                </div>
              </div>
              <div className="flex items-center flex-1 min-w-[150px]">
                <div className="flex-grow">
                  <StyleSelector />
                </div>
              </div>
              <div className="flex items-center flex-1 min-w-[150px]">
                <div className="flex-grow">
                  <SizeSelector />
                </div>
              </div>
            </div>
          </div>

          {/* Summary content, loading state, or empty state */}
          {loading ? (
            <LoadingSummaryState />
          ) : post ? (
            <SummaryDisplay />
          ) : (
            <EmptySummaryState />
          )}
        </div>

        {/* Transcript Tab Content */}
        <div className={`transition-opacity duration-200 ${activeTab === 'transcript' ? 'block' : 'hidden'}`}>
          <div 
            className="rounded-md overflow-y-auto max-h-[calc(100vh-350px)] pr-2 [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-thumb]:bg-gray-600 hover:[&::-webkit-scrollbar-thumb]:bg-gray-400 dark:hover:[&::-webkit-scrollbar-thumb]:bg-gray-500"
            style={{
              fontFamily: 'var(--font-inter)',
              fontSize: '16px',
              fontStyle: 'normal',
              fontWeight: 400,
              letterSpacing: '-0.08px',
              lineHeight: '22px',
              scrollbarWidth: 'thin',
              scrollbarColor: 'var(--gray-300) transparent',
              msOverflowStyle: 'none'  /* IE and Edge */
            }}
          >
            {/* Show loading indicator, transcript, or empty message */}
            {loading ? (
              <div className="flex flex-col items-center justify-center py-8">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mb-4"></div>
                <p className="text-gray-500 dark:text-gray-400">Loading transcript...</p>
              </div>
            ) : transcript ? (
              <div className="pr-2">
                {/* Using MarkdownRenderer for consistent formatting */}
                <MarkdownRenderer content={transcript} />
                
                <div className="flex gap-2 mt-4">
                  <Button
                    onClick={() => {
                      navigator.clipboard.writeText(transcript || '');
                    }}
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-1"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <rect width="14" height="14" x="8" y="8" rx="2" ry="2" />
                      <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2" />
                    </svg>
                    Copy Transcript
                  </Button>
                </div>
              </div>
            ) : (
              <p className="text-gray-500 dark:text-gray-400 text-center py-8">No transcript available yet.</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
