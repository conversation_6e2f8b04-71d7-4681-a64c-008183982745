'use client';

import Select from '../ui/SelectPlain';
import { useFormContext } from './FormContext';
import { OutputLanguage } from '@/app/actions';

export default function LanguageSelector() {
  const { language, setLanguage } = useFormContext();

  const languageOptions = [
    { value: 'english', label: 'English' },
    { value: 'hindi', label: 'Hindi' },
    { value: 'spanish', label: 'Spanish' },
    { value: 'french', label: 'French' },
    { value: 'german', label: 'German' },
    { value: 'chinese', label: 'Chinese' },
    { value: 'japanese', label: 'Japanese' }
  ];

  return (
    <Select
      id="language"
      value={language}
      onChange={(value) => setLanguage(value as OutputLanguage)}
      options={languageOptions}
      title="Select the language for your summary output"
    />
  );
}
