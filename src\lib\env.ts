// Environment variables validation
export const env = {
  // OpenAI API key for <PERSON>hisper and GPT
  OPENAI_API_KEY: process.env.OPENAI_API_KEY || '',

  // Environment configuration
  NODE_ENV: process.env.NODE_ENV || 'development',

  // Logging configuration
  LOG_LEVEL: process.env.LOG_LEVEL || 'INFO',

  // Performance configuration
  MAX_API_RETRIES: parseInt(process.env.MAX_API_RETRIES || '3', 10),

  // OpenAI model configuration
  GPT_MODEL: process.env.GPT_MODEL || 'gpt-4o-2024-05-13',
  GPT_MODEL_FREE: process.env.GPT_MODEL_FREE || 'gpt-3.5-turbo',
  GPT_MODEL_PRO: process.env.GPT_MODEL_PRO || 'gpt-4o-2024-05-13',
  GPT_MODEL_MINI: process.env.GPT_MODEL_MINI || 'gpt-4o-mini',

  // Feature flags
  USE_AUDIO_WHISPER: process.env.USE_AUDIO_WHISPER === 'true',
};

// Validate required environment variables
export function validateEnv() {
  // OpenAI API key is always required
  if (!env.OPENAI_API_KEY) {
    throw new Error('Missing environment variable: OPENAI_API_KEY');
  }

  // Check if the API key is the placeholder value
  if (env.OPENAI_API_KEY === 'your_actual_openai_api_key_here') {
    throw new Error('Please replace the placeholder OpenAI API key with your actual key in .env.local');
  }

  // Define valid GPT models
  const validGptModels = ['gpt-4o-2024-05-13', 'gpt-4o', 'gpt-4o-mini', 'gpt-4', 'gpt-3.5-turbo'];

  // Validate main GPT model
  if (!validGptModels.includes(env.GPT_MODEL)) {
    console.warn(`Warning: Unrecognized GPT model "${env.GPT_MODEL}". Using default model "gpt-4o-2024-05-13" instead.`);
    (env as any).GPT_MODEL = 'gpt-4o-2024-05-13';
  }

  // Validate free tier GPT model
  if (!validGptModels.includes(env.GPT_MODEL_FREE)) {
    console.warn(`Warning: Unrecognized free tier GPT model "${env.GPT_MODEL_FREE}". Using default model "gpt-3.5-turbo" instead.`);
    (env as any).GPT_MODEL_FREE = 'gpt-3.5-turbo';
  }

  // Validate pro tier GPT model
  if (!validGptModels.includes(env.GPT_MODEL_PRO)) {
    console.warn(`Warning: Unrecognized pro tier GPT model "${env.GPT_MODEL_PRO}". Using default model "gpt-4o-2024-05-13" instead.`);
    (env as any).GPT_MODEL_PRO = 'gpt-4o-2024-05-13';
  }

  // Validate mini tier GPT model
  if (!validGptModels.includes(env.GPT_MODEL_MINI)) {
    console.warn(`Warning: Unrecognized mini tier GPT model "${env.GPT_MODEL_MINI}". Using default model "gpt-4o-mini" instead.`);
    (env as any).GPT_MODEL_MINI = 'gpt-4o-mini';
  }
}
