import type { Metada<PERSON> } from "next";
import localFont from "next/font/local";
import "./globals.css";
import { Footer } from "@/components/Footer";
import { Navigation } from "@/components/Navigation";

const geistSans = localFont({
  src: "./fonts/GeistVF.woff",
  variable: "--font-geist-sans",
  weight: "100 900",
});

const geistMono = localFont({
  src: "./fonts/GeistMonoVF.woff",
  variable: "--font-geist-mono",
  weight: "100 900",
});

export const metadata: Metadata = {
  title: {
    default: "Nobot - AI-Powered Content Summarization",
    template: "%s | Nobot"
  },
  description: "Transform lengthy content into concise, actionable insights with Nobot's AI-powered summarization technology. Save time and boost productivity.",
  keywords: ["AI", "summarization", "productivity", "content analysis", "artificial intelligence"],
  authors: [{ name: "Nobot Team" }],
  creator: "Nobot",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://nobot.ai",
    title: "Nobot - AI-Powered Content Summarization",
    description: "Transform lengthy content into concise, actionable insights with AI",
    siteName: "Nobot",
  },
  twitter: {
    card: "summary_large_image",
    title: "Nobot - AI-Powered Content Summarization",
    description: "Transform lengthy content into concise, actionable insights with AI",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <div className="min-h-screen bg-gray-900 text-white">
          <Navigation />
          {children}
          <Footer />
        </div>
      </body>
    </html>
  );
}
