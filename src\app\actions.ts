'use server';

import ytdl from '@distube/ytdl-core';
import OpenAI from 'openai';
import { join } from 'path';
import { mkdir } from 'fs/promises';
import { existsSync } from 'fs';
import { env, validateEnv } from '@/lib/env';
import * as logger from '@/lib/logger';
import { LogLevel } from '@/lib/logger';
import { Innertube } from 'youtubei.js/web';

// Validate environment variables
validateEnv();

// Simple constant to define which model to use throughout the application
const SELECTED_MODEL = env.GPT_MODEL_MINI; // Change this to use different models: env.GPT_MODEL_FREE, env.GPT_MODEL_PRO, env.GPT_MODEL_MINI

// Flag to control pipeline mode
const multiStepPipeline = false; // If true: translate to English → summarize → translate to target. If false: directly summarize in target language

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: env.OPENAI_API_KEY,
});

// Define output styles
export type OutputStyle = 'insightful' | 'funny' | 'actionable' | 'controversial' | 'educational' | 'technical' | 'eli5';

// Define output size
export type OutputSize = 'concise' | 'auto' | 'detailed';

// Define output languages
export type OutputLanguage = 'english' | 'hindi' | 'spanish' | 'french' | 'german' | 'chinese' | 'japanese';

// Create temp directory if it doesn't exist
const ensureTempDir = async () => {
  const tempDir = join(process.cwd(), 'temp');
  if (!existsSync(tempDir)) {
    await mkdir(tempDir, { recursive: true });
  }
  return tempDir;
};

// Download audio from YouTube video
export async function downloadYouTubeAudio(videoUrl: string): Promise<string> {
  logger.startTimer('downloadYouTubeAudio');
  logger.info(`[STEP 1/5] Starting YouTube audio download for URL: ${videoUrl}`);

  try {
    // Validate YouTube URL
    logger.startTimer('validateURL');
    if (!ytdl.validateURL(videoUrl)) {
      logger.error('Invalid YouTube URL provided', { videoUrl });
      throw new Error('Invalid YouTube URL');
    }
    logger.endTimer('validateURL');

    // Get video info
    logger.startTimer('getVideoInfo');
    logger.info('Fetching video information from YouTube');
    const info = await ytdl.getInfo(videoUrl);
    const videoId = info.videoDetails.videoId;
    const videoTitle = info.videoDetails.title;
    const videoDuration = info.videoDetails.lengthSeconds;
    logger.endTimer('getVideoInfo', LogLevel.INFO);

    // Only log basic video info at INFO level, detailed info at DEBUG level
    logger.info(`Video information retrieved: ${videoTitle}`);
    logger.debug(() => `Video details:`, {
      videoId,
      title: videoTitle,
      duration: `${videoDuration}s`
    });

    // Warn if video is long
    if (parseInt(videoDuration) > 600) { // 10 minutes
      logger.warn(`Long video detected (${Math.floor(parseInt(videoDuration) / 60)} minutes). Processing may take longer than usual.`);
    }

    // Ensure temp directory exists
    logger.startTimer('ensureTempDir');
    const tempDir = await ensureTempDir();
    const audioPath = join(tempDir, `${videoId}.mp3`);
    logger.endTimer('ensureTempDir');

    // Download audio only
    logger.startTimer('audioDownload');
    logger.info('Starting audio download stream');
    return new Promise((resolve, reject) => {
      // Use audioonly filter to get audio-only streams
      const audioStream = ytdl(videoUrl, {
        filter: 'audioonly'  // This will filter for audio-only streams
      });

      // Create write stream
      const writeStream = require('fs').createWriteStream(audioPath);

      // Track download progress
      let downloadedBytes = 0;
      let lastLogTime = Date.now();
      let downloadStarted = false;
      let logCount = 0; // Track how many progress logs we've made

      audioStream.on('progress', (_, downloaded, total) => {
        if (!downloadStarted) {
          downloadStarted = true;
          logger.debug('Download started receiving data');
        }

        downloadedBytes = downloaded;

        // Log progress every 5 seconds to avoid flooding logs
        // But reduce frequency further after the first few logs
        const now = Date.now();
        const timeSinceLastLog = now - lastLogTime;
        const logInterval = logCount < 3 ? 5000 : 10000; // Increase interval after first few logs

        if (timeSinceLastLog > logInterval) {
          lastLogTime = now;
          logCount++;

          // Only calculate these values if we're actually going to log
          if (logger.shouldLog(LogLevel.INFO)) {
            const percentage = (downloaded / total * 100).toFixed(2);
            const downloadedMB = (downloaded / 1024 / 1024).toFixed(2);
            const totalMB = (total / 1024 / 1024).toFixed(2);
            logger.info(`Download progress: ${percentage}% (${downloadedMB}/${totalMB} MB)`);
          }
        }
      });

      // Pipe audio to file
      audioStream.pipe(writeStream);

      // Handle completion
      writeStream.on('finish', () => {
        logger.endTimer('audioDownload', LogLevel.INFO);
        logger.info(`Audio download completed: ${audioPath}`, {
          fileSize: `${(downloadedBytes / 1024 / 1024).toFixed(2)} MB`,
          videoId
        });
        logger.endTimer('downloadYouTubeAudio', LogLevel.INFO);
        resolve(audioPath);
      });

      // Handle errors
      audioStream.on('error', (err: Error) => {
        logger.error('Error in audio stream', { error: err.message });
        reject(err);
      });

      writeStream.on('error', (err: Error) => {
        logger.error('Error in write stream', { error: err.message });
        reject(err);
      });
    });
  } catch (error: any) {
    logger.error('Error downloading YouTube audio', {
      error: error.message
    });
    // Log stack trace only at debug level
    logger.debug('Error stack trace:', { stack: error.stack });
    throw error;
  }
}

// Extract video ID from YouTube URL
function extractVideoId(videoUrl: string): string | null {
  if (!videoUrl) return null;
  
  if (videoUrl.includes('youtube.com') && videoUrl.includes('v=')) {
    const match = videoUrl.match(/v=([^&#]*)/);
    return match ? match[1] : null;
  } else if (videoUrl.includes('youtu.be/')) {
    const match = videoUrl.match(/youtu.be\/([^?#]*)/);
    return match ? match[1] : null;
  }
  
  // If it's just the video ID
  return videoUrl.length === 11 ? videoUrl : null;
}

// Attempt to fetch YouTube transcript/captions using youtubei.js
export async function fetchYouTubeTranscript(videoUrl: string): Promise<string | null> {
  logger.info(`[STEP 1/5] Attempting to fetch YouTube captions/transcript for URL: ${videoUrl}`);
  
  const videoId = extractVideoId(videoUrl);
  if (!videoId) {
    logger.error('Invalid YouTube URL or video ID');
    return null;
  }

  logger.info(`Attempting to fetch captions/transcript for video ID: ${videoId}`);
  
  const maxRetries = env.MAX_API_RETRIES ?? 3;
  let retries = 0;
  let lastError: Error | null = null;

  while (retries < maxRetries) {
    try {
      logger.info(`Fetching YouTube transcript (attempt ${retries + 1}/${maxRetries})...`);
      
      // Initialize YouTube client
      const youtube = await Innertube.create({
        lang: 'en',
        location: 'US',
        retrieve_player: false,
      });

      // Get video info and transcript
      const info = await youtube.getInfo(videoId);
      const transcriptData = await info.getTranscript();
      
      if (transcriptData?.transcript?.content?.body?.initial_segments?.length > 0) {
        // Format transcript with timestamps in a cleaner format
        const transcriptWithTimestamps = transcriptData.transcript.content.body.initial_segments
          .map((segment: any) => {
            const startTime = segment.start_time_text || '';
            const text = segment.snippet.text || '';
            return startTime ? `${startTime}\n${text}` : text;
          })
          .join('\n\n');

        logger.info(`YouTube captions/transcript fetched successfully: ${transcriptWithTimestamps.length} characters`);
        logger.debug('Caption/transcript preview:', {
          preview: transcriptWithTimestamps.substring(0, 100) + '...'
        });

        return transcriptWithTimestamps;
      } else {
        logger.warn('No transcript segments found in the response');
        retries++;
        if (retries >= maxRetries) {
          logger.warn('Max retries reached. YouTube captions/transcript not available.');
          return null;
        }
        return null;
      }
    } catch (error: any) {
      lastError = error;
      retries++;
      logger.warn(`Failed to fetch YouTube captions/transcript (attempt ${retries}/${maxRetries}): ${error.message}`);

      if (retries >= maxRetries) {
        logger.warn('Max retries reached. YouTube captions/transcript fetching failed.');
        return null;
      }

      // Wait before retrying
      const waitTime = retries * 1000; // Increasing backoff
      logger.info(`Waiting ${waitTime}ms before retrying...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }

  logger.warn(`All attempts to fetch YouTube captions failed: ${lastError?.message || 'Unknown error'}`);
  return null;
}

// Transcribe audio using OpenAI Whisper with automatic language detection
export async function transcribeAudio(
  audioPath: string,
  outputLanguage: string = 'english' // We keep this parameter for compatibility but don't use it for transcription
): Promise<string> {
  logger.startTimer('transcribeAudio');
  logger.info(`[STEP 1/5] Starting audio transcription for file: ${audioPath}`);

  // Only log this at debug level since it's not critical information
  logger.debug(`Using automatic language detection (output language: ${outputLanguage})`);

  // Check if file exists
  const fs = require('fs');
  if (!fs.existsSync(audioPath)) {
    logger.error(`Audio file not found at path: ${audioPath}`);
    throw new Error(`Audio file not found: ${audioPath}`);
  }

  // Log file stats - only detailed stats at debug level
  try {
    const stats = fs.statSync(audioPath);
    const fileSizeMB = (stats.size / 1024 / 1024).toFixed(2);

    // Basic info at INFO level
    logger.info(`Audio file size: ${fileSizeMB} MB`);

    // Detailed info at DEBUG level
    logger.debug(() => `Audio file details:`, {
      size: `${fileSizeMB} MB`,
      created: stats.birthtime,
      path: audioPath
    });

    // Warn if file is large
    if (parseFloat(fileSizeMB) > 10) {
      logger.warn(`Large audio file detected (${fileSizeMB} MB). Transcription may take longer than usual.`);
    }
  } catch {
    logger.warn(`Could not get file stats for ${audioPath}`);
  }

  // Use OpenAI Whisper for transcription
  logger.info('Using OpenAI Whisper for transcription');
  const maxRetries = env.MAX_API_RETRIES;
  let retries = 0;

  while (retries < maxRetries) {
    try {
      logger.startTimer(`transcriptionAttempt${retries + 1}`);
      logger.info(`Attempting to transcribe with OpenAI Whisper (attempt ${retries + 1}/${maxRetries})...`);

      // Create a new read stream for each attempt
      const fileStream = fs.createReadStream(audioPath);

      // Use auto-detection for transcription regardless of output language
      logger.info('Calling OpenAI Whisper API - this may take several minutes for longer audio files');
      logger.startTimer('openaiApiCall');

      const transcription = await openai.audio.transcriptions.create({
        file: fileStream,
        model: 'whisper-1',
        // Not specifying language parameter to use auto-detection
      });

      logger.endTimer('openaiApiCall', LogLevel.INFO);

      // Using default response format, the API returns an object with a text property
      if (transcription && transcription.text) {
        logger.endTimer(`transcriptionAttempt${retries + 1}`, LogLevel.INFO);
        logger.info(`Transcription successful with ${transcription.text.length} characters`);

        // Only log preview at debug level
        if (logger.shouldLog(LogLevel.DEBUG)) {
          const preview = transcription.text.substring(0, 100) + (transcription.text.length > 100 ? '...' : '');
          logger.debug('Transcript preview:', { preview });
        }

        logger.endTimer('transcribeAudio', LogLevel.INFO);
        return transcription.text;
      } else {
        logger.warn('Transcription returned empty text');
        throw new Error('Transcription returned empty text');
      }
    } catch (error: any) {
      retries++;

      // Log basic error at WARN level
      logger.warn(`Error transcribing audio (attempt ${retries}/${maxRetries}): ${error.message}`);

      // Log detailed error info at DEBUG level
      logger.debug(`Transcription error details:`, {
        error: error.message,
        code: error.code,
        status: error.status
      });

      // If we've reached max retries, throw the error
      if (retries >= maxRetries) {
        logger.error('Max retries reached. OpenAI transcription failed.');

        // Provide a more user-friendly error message
        if (error.code === 'ECONNRESET') {
          logger.error('Connection reset error occurred with OpenAI API');
          throw new Error('Connection to OpenAI was reset. Please check your internet connection and try again.');
        } else if (error.status === 403 || error.message?.includes('403') || error.message?.includes('Forbidden')) {
          logger.error('OpenAI API access forbidden (403 error)');
          logger.debug('OpenAI API error details:', { errorDetails: error });
          throw new Error('OpenAI API access forbidden (403 error). This usually means your OpenAI account does not have billing set up, which is required for using the Whisper API, or your API key is invalid. Please check your OpenAI account settings.');
        } else {
          logger.error('Unknown transcription error');
          logger.debug('Error details:', { errorDetails: error });
          throw new Error(`Failed to transcribe audio: ${error.message || 'Unknown error'}`);
        }
      }

      // Wait before retrying
      const waitTime = retries * 2;
      logger.info(`Retrying in ${waitTime} seconds...`);
      await new Promise(resolve => setTimeout(resolve, waitTime * 1000));
    }
  }

  // This should never be reached due to the throw in the catch block
  logger.error('Unexpected code path reached in transcription');
  throw new Error('Failed to transcribe audio after multiple attempts');
}

// Translate text to English using OpenAI
export async function translateToEnglish(
  text: string
): Promise<{ translatedText: string; modelUsed: string }> {
  logger.startTimer('translateToEnglish');
  logger.info(`[TRANSLATION] Translating text to English (${text.length} characters)`);

  try {
    // Use the selected model
    const modelToUse = SELECTED_MODEL;

    const prompt = `You are a professional translator. Translate the following text to English while preserving all meaning, context, and nuances. If the text is already in English, return it unchanged.

IMPORTANT INSTRUCTIONS:
- Maintain all technical terms, names, and specific details
- Preserve the original structure and formatting
- Keep all timestamps, numbers, and data points exactly as they are
- If already in English, return the exact same text
- Do not add any commentary or explanations

Text to translate:
"""
${text}
"""`;

    logger.info(`Calling OpenAI for English translation with ${modelToUse} model`);
    logger.startTimer('openaiTranslateCall');

    const completion = await openai.chat.completions.create({
      messages: [{ role: 'user', content: prompt }],
      model: modelToUse,
    });

    logger.endTimer('openaiTranslateCall', LogLevel.INFO);

    if (completion.choices[0]?.message?.content) {
      const translatedText = completion.choices[0].message.content.trim();

      logger.info(`Translation to English completed: ${translatedText.length} characters`);
      logger.endTimer('translateToEnglish', LogLevel.INFO);

      return {
        translatedText,
        modelUsed: modelToUse
      };
    } else {
      logger.error('Empty response from OpenAI API during translation');
      throw new Error('Failed to translate to English: Empty response from OpenAI');
    }
  } catch (error: any) {
    logger.error(`Error translating to English: ${error.message}`);
    logger.debug('Translation error details:', {
      error: error.message,
      code: error.code,
      status: error.status
    });
    logger.endTimer('translateToEnglish');
    throw new Error(`Translation to English failed: ${error.message}`);
  }
}

// Translate text from English to target language using OpenAI
export async function translateToTargetLanguage(
  text: string,
  targetLanguage: OutputLanguage
): Promise<{ translatedText: string; modelUsed: string }> {
  logger.startTimer('translateToTargetLanguage');
  logger.info(`[TRANSLATION] Translating text from English to ${targetLanguage} (${text.length} characters)`);

  // If target language is English, return as-is
  if (targetLanguage === 'english') {
    logger.info('Target language is English, returning original text');
    logger.endTimer('translateToTargetLanguage', LogLevel.INFO);
    return {
      translatedText: text,
      modelUsed: 'no-translation-needed'
    };
  }

  try {
    // Use the selected model
    const modelToUse = SELECTED_MODEL;

    const prompt = `You are a professional translator specializing in high-quality translations that preserve meaning, tone, and structure. Translate the following English text to ${targetLanguage}.

CRITICAL TRANSLATION REQUIREMENTS:
- Maintain the exact same structure and formatting (headings, bullet points, etc.)
- Preserve all technical terms and proper nouns appropriately
- Keep the same tone and style (professional, educational, etc.)
- Maintain all numbers, percentages, and data points exactly
- Ensure cultural appropriateness for ${targetLanguage} speakers
- Use natural, fluent ${targetLanguage} that reads like native content
- Do not add any explanations or commentary

English text to translate:
"""
${text}
"""`;

    logger.info(`Calling OpenAI for ${targetLanguage} translation with ${modelToUse} model`);
    logger.startTimer('openaiTargetTranslateCall');

    const completion = await openai.chat.completions.create({
      messages: [{ role: 'user', content: prompt }],
      model: modelToUse,
    });

    logger.endTimer('openaiTargetTranslateCall', LogLevel.INFO);

    if (completion.choices[0]?.message?.content) {
      const translatedText = completion.choices[0].message.content.trim();

      logger.info(`Translation to ${targetLanguage} completed: ${translatedText.length} characters`);
      logger.endTimer('translateToTargetLanguage', LogLevel.INFO);

      return {
        translatedText,
        modelUsed: modelToUse
      };
    } else {
      logger.error('Empty response from OpenAI API during target language translation');
      throw new Error(`Failed to translate to ${targetLanguage}: Empty response from OpenAI`);
    }
  } catch (error: any) {
    logger.error(`Error translating to ${targetLanguage}: ${error.message}`);
    logger.debug('Target translation error details:', {
      error: error.message,
      code: error.code,
      status: error.status
    });
    logger.endTimer('translateToTargetLanguage');
    throw new Error(`Translation to ${targetLanguage} failed: ${error.message}`);
  }
}

// Generate comprehensive summary directly in target language (single-step pipeline)
export async function generateDirectSummary(
  transcript: string,
  language: OutputLanguage,
  style: OutputStyle,
  size: OutputSize = 'auto'
): Promise<{ summary: string; modelUsed: string }> {
  logger.startTimer('generateDirectSummary');
  logger.info(`[SINGLE-STEP] Generating comprehensive summary directly in ${language} with ${style} style and ${size} size`);

  // Only log transcript length at debug level
  if (logger.shouldLog(LogLevel.DEBUG)) {
    logger.debug(`Transcript length: ${transcript.length} characters`);
  }

  try {
    // Create prompt based on user preferences
    logger.startTimer('createDirectPrompt');

    // Get language name for the prompt
    const languageNames = {
      english: 'English',
      hindi: 'Hindi',
      spanish: 'Spanish',
      french: 'French',
      german: 'German',
      chinese: 'Chinese',
      japanese: 'Japanese'
    };
    const targetLanguageName = languageNames[language] || 'English';

    // Use the same style descriptions as the multi-step pipeline
    // Style Descriptions for GPT-4
const styleDescription = {
  insightful: `- Professional and insightful tone suitable for a business or intellectual audience
- Use thoughtful and analytical emojis (🧠, 💡, 🔍, 🤔, 📊, 🧩, etc.)
- Emphasize deeper meanings, patterns, and connections between concepts
- Highlight intellectual frameworks, mental models, and underlying principles
- Structure content for maximum depth and understanding
- Focus on "why" questions and root causes rather than just surface observations
- Connect ideas to broader contexts and implications
- Include thought-provoking questions or considerations where appropriate`,
  eli5: `- Use extremely simple and clear language that anyone can understand
- Avoid technical jargon, complex terms, and difficult vocabulary
- Break down complicated concepts into basic, easy-to-follow explanations
- Use everyday analogies and simple comparisons when helpful
- Keep sentences short and straightforward
- Explain "why" and "how" things work in the simplest way possible
- Use minimal, relevant emojis only when they add clarity
- Focus on making complex ideas accessible without losing important details
- Ensure comprehensive coverage while maintaining simplicity`,
  funny: `- Light-hearted and humorous tone with a conversational feel
- Use fun and playful emojis (😂, 🤣, 😅, 🙃, 🤪, 🎭, etc.)
- Emphasize amusing observations, ironies, and entertaining aspects
- Include witty remarks, clever wordplay, and humorous analogies
- Structure content for maximum enjoyment and shareability
- Use casual language and conversational phrases
- Incorporate appropriate humor while still conveying all key information
- Balance humor with informative content - don't sacrifice substance for jokes`,
  actionable: `- Practical and solution-oriented tone focused on implementation
- Use action-oriented emojis (✅, 🎯, 🚀, 💪, 🔧, 📝, etc.)
- Emphasize specific steps, methods, techniques, and applications
- Highlight clear opportunities for implementation and practical takeaways
- Structure content for maximum utility and immediate application
- Use imperative verbs and direct instructions where appropriate
- Break down complex processes into manageable steps
- Focus on "how-to" aspects and practical applications of concepts
- Include specific, concrete examples of implementation`,
  controversial: `- Balanced presentation of different perspectives with a neutral tone
- Use thought-provoking emojis (⚖️, 🔄, 🤝, 🧐, 💭, ❓, etc.)
- Emphasize points of debate, tensions, and different viewpoints
- Highlight areas of disagreement, competing ideas, and alternative interpretations
- Structure content to show multiple sides of complex issues
- Present contrasting viewpoints with equal depth and fairness
- Avoid taking sides while still providing comprehensive analysis
- Acknowledge the nuances and complexities of debated topics
- Include the strongest arguments from each perspective`,
  educational: `- Clear, structured, and informative tone optimized for learning
- Use educational and explanatory emojis (📚, 🎓, ✏️, 📝, 🔬, 📊, etc.)
- Emphasize learning points, key concepts, and foundational principles
- Highlight definitions, classifications, and hierarchical relationships
- Structure content for progressive understanding and knowledge building
- Present information in a logical sequence that builds understanding
- Define specialized terminology clearly before using it
- Use examples and analogies to illustrate complex concepts
- Include summative statements that reinforce key learning points`,
  technical: `- Technical and precise tone with specialized vocabulary
- Use industry-specific and technical emojis (⚙️, 💻, 🔧, 📊, 🧮, 🔬, etc.)
- Emphasize specifications, methodologies, technical details, and processes
- Maintain precise terminology, technical accuracy, and domain-specific language
- Structure for maximum technical value and specialized understanding
- Include relevant technical parameters, measurements, and specifications
- Preserve technical nuances and distinctions between similar concepts
- Use appropriate technical frameworks and models from the domain
- Balance technical depth with clarity for the intended audience`,
};

// Simpler descriptions for GPT-3.5
const simpleStyleDescription = {
  insightful: `- Use an intellectual tone and thoughtful emojis
- Focus on deeper ideas, connections, and why things matter
- Highlight patterns, principles, and broader meaning
- Ask smart questions and reflect on big-picture thinking`,
  eli5: `- Use very simple and clear language that everyone can understand
- Avoid complex words and technical terms
- Break down difficult concepts into easy explanations
- Use simple examples and analogies when needed`,
  funny: `- Use a casual and fun tone with playful emojis
- Add jokes, wordplay, and light commentary
- Make it enjoyable and entertaining without losing the message`,
  actionable: `- Use clear steps and practical suggestions
- Include action-based emojis and tips
- Help users apply the content with useful takeaways`,
  controversial: `- Show both sides of any issue fairly
- Use balanced language and thought-provoking emojis
- Explain different views and their reasoning`,
  educational: `- Use a teaching tone and clear examples
- Add learning emojis and build understanding step by step
- Explain new terms and reinforce main ideas`,
  technical: `- Use clear and accurate technical terms
- Add relevant emojis and focus on how things work
- Stick to the facts and include detailed explanations`,
};

// Length modifiers
const sizeModifiers = {
  concise: `- Keep the summary brief and focused on only the most essential points
- Aim for approximately 30-40% shorter than a standard summary
- Prioritize the most impactful information
- Limit the NARRATIVE OVERVIEW to 1 paragraph maximum
- Include 5-8 HIGHLIGHTS instead of the standard number
- Include 3-5 KEY INSIGHTS instead of the standard number
- Be selective about which points to include, focusing on the highest-value information
- Omit secondary details while preserving all critical information
- Use concise language and avoid unnecessary elaboration`,
  detailed: `- Create a comprehensive summary that covers all significant details
- Include more examples and supporting information
- Aim for approximately 30-40% longer than a standard summary
- Expand the NARRATIVE OVERVIEW to 2-3 paragraphs for thorough context
- Include 10-15 HIGHLIGHTS to ensure comprehensive coverage
- Include 6-9 KEY INSIGHTS for deeper analysis
- Include secondary details that add valuable context or nuance
- Provide more examples and evidence to support main points
- Explore implications and connections more thoroughly
- Ensure no significant information from the transcript is omitted`,
  auto: `- Automatically adjust the length based on content complexity and importance
- Use your judgment to determine the appropriate level of detail
- Balance brevity with comprehensiveness based on content importance
- For complex or information-dense content, provide more detail
- For simpler or more straightforward content, be more concise
- Adjust the number of HIGHLIGHTS and KEY INSIGHTS based on content richness
- Prioritize clarity and value over arbitrary length constraints
- Ensure the summary length is proportional to the information density of the original
- Focus on providing maximum value to the reader regardless of length`,
};

// Valid keys for lookup
const styleOptions = Object.keys(styleDescription);
const sizeOptions = Object.keys(sizeModifiers);

// Detect if GPT-4 model (including GPT-4o-mini)
const isGpt4oModel = SELECTED_MODEL.includes('gpt-4o');

// Helper to get style safely
const getStyleDescription = (style: string) =>
  (isGpt4oModel ? styleDescription : simpleStyleDescription as any)[styleOptions.includes(style) ? style : 'educational'];

// Helper to get size modifier safely
const getSizeModifier = (size: string) =>
  (sizeModifiers as any)[sizeOptions.includes(size) ? size : 'auto'];

// Selected modifiers
const selectedStyleDescription = getStyleDescription(style);
const selectedSizeModifier = getSizeModifier(size);

    // Create direct summarization prompt
    const directPrompt = `
You are an elite content strategist and summarization expert, trained to extract **maximum educational value** from long-form YouTube video transcripts. You write summaries that feel **handcrafted by domain experts**, not by generic AI.

🎯 OBJECTIVE:
Generate an **exceptionally detailed, highly readable, and fully self-contained summary** of the **YouTube video transcript** provided below. Your summary should be better than NoteGPT, ChatGPT, or any automated summary tool — in **quality, insight, and completeness**.

${style === 'eli5' ? `
🎪 WHAT WE'LL MAKE TOGETHER:
Let's organize this like a fun adventure book with these parts:

---

1. 🌟 COOL THINGS I LEARNED (8-15 fun facts)
   - Start each one with a fun emoji like 🎈 🦄 🚀 🎨 🍭
   - Tell me the coolest things from the video in super simple words
   - Make each fact feel like a fun surprise or "wow!" moment
   - Use examples with things kids love (like animals, toys, games, food)
   - Make sure I can understand each fact all by itself

---

2. 🧠 BIG IDEAS (5-10 important lessons)
   - Start each with an emoji and explain it like a story
   - Help me understand why these things are important
   - Use "imagine if..." and "it's like when..." examples
   - Connect it to things I already know and love
   - Make the big ideas feel exciting and magical

---

3. 🎯 THE MAIN IDEA (1-2 simple sentences)
   - Tell me what the whole video was about in the easiest way possible
   - Use words a 5-year-old would understand

---

4. 📖 THE WHOLE STORY (2-3 short paragraphs)
   - Tell me the video like it's a bedtime story
   - Start from the beginning and go to the end
   - Use simple words and make it fun to read
   - Help me understand what happened and why it's cool` : `
📦 OUTPUT STRUCTURE:
Use these exact headings and formats to ensure structure, scannability, and clarity:

---

1. ✨ HIGHLIGHTS (10–20 points)
   - Begin each point with a **relevant emoji only** (no dashes or bullets)
   - Every point must be **standalone**, clear without needing extra context
   - Include **detailed facts, named individuals, key stats, and exact concepts**
   - Each point should feel like a "quotable insight" or a "knowledge nugget"
   - Avoid vague phrases — **make every word count**
   - Cover the **entire length of the video**, not just the beginning

---

2. 🧠 KEY INSIGHTS (10–20 points)
   - Start each point with an emoji
   - Extract **big-picture ideas**, recurring patterns, and **universal principles**
   - Include **mental models**, cause-effect relationships, and strategic takeaways
   - Help the reader connect content to **real-world relevance or application**
   - Show **why** certain things matter — not just what was said

---

3. 📝 TL;DR SUMMARY (1–2 sentences)
   - Craft a tight, high-clarity overview of the video's **core message**
   - Include key context and conclusions in a clear, fluid sentence

---

4. 📚 NARRATIVE OVERVIEW (2–4 short paragraphs)
   - Retell the **video's full arc** from intro to conclusion
   - Maintain an **educational tone**; echo the original speaker's intent and voice
   - Summarize subtopics in **chronological or logical flow**
   - Provide missing background context if needed
   - This section should feel like a **mini article** or an "executive digest"`}

---

🎨 STYLE GUIDELINES:
- Use spaced-out blocks for **easy skimming**
- **Avoid nested bullets or in-paragraph lists**
- Match the video's tone — technical, conversational, motivational, etc.
- Every point must be **independently valuable** to the reader
- Assume the reader has **no access to the video** — all context must be included

📏 LENGTH + DEPTH:
${selectedSizeModifier}

🧠 STYLE & TONE:
${selectedStyleDescription}

✅ QUALITY CHECKLIST:
- No made-up content — rely **strictly on the transcript**
- Capture **everything meaningful**, even subtle points or minor arguments
- Include **specific numbers, named people, events, quotes, or locations**
- Ensure every bullet, insight, and paragraph is **dense with real value**

🌍 LANGUAGE:
Write the entire summary in **${targetLanguageName}** — with professional fluency and clarity. If the transcript is in a different language, understand it fully and then write your summary in ${targetLanguageName}.

---

🎥 TRANSCRIPT INPUT:
"""
${transcript}
"""
`;

    logger.endTimer('createDirectPrompt');

    // Only log prompt details at debug level
    logger.debug(() => 'Direct prompt details:', {
      promptLength: directPrompt.length,
      language,
      style
    });

    // Use the selected model
    const modelToUse = SELECTED_MODEL;

    // Call OpenAI API
    logger.info(`Calling OpenAI Chat Completions API with ${modelToUse} model for direct ${language} summary`);
    logger.startTimer('openaiDirectCall');

    const completion = await openai.chat.completions.create({
      messages: [{ role: 'user', content: directPrompt }],
      model: modelToUse,
    });

    logger.endTimer('openaiDirectCall', LogLevel.INFO);

    if (completion.choices && completion.choices.length > 0 && completion.choices[0].message.content) {
      const summary = completion.choices[0].message.content;
      logger.info(`Direct summary generated successfully (${summary.length} characters)`);

      // Only log preview at debug level
      if (logger.shouldLog(LogLevel.DEBUG)) {
        const preview = summary.substring(0, 100) + (summary.length > 100 ? '...' : '');
        logger.debug('Direct summary preview:', { preview });
      }

      // Log usage information only at debug level
      if (completion.usage) {
        logger.debug('API usage stats:', {
          promptTokens: completion.usage.prompt_tokens,
          completionTokens: completion.usage.completion_tokens,
          totalTokens: completion.usage.total_tokens
        });
      }

      logger.endTimer('generateDirectSummary', LogLevel.INFO);

      return {
        summary: summary.replace(/^\s*[-•]\s+/gm, ''),
        modelUsed: modelToUse
      };
    } else {
      logger.error('Empty response from OpenAI API');
      throw new Error('Failed to generate direct summary: Empty response from OpenAI');
    }
  } catch (error: any) {
    // Log basic error at ERROR level
    logger.error(`Error generating direct summary: ${error.message}`);

    // Log detailed error info at DEBUG level
    logger.debug('Direct summary generation error details:', {
      error: error.message,
      code: error.code,
      status: error.status
    });

    // Provide a more user-friendly error message
    if (error.code === 'ECONNRESET') {
      throw new Error('Connection to OpenAI was reset. Please check your internet connection and try again.');
    } else {
      throw new Error(`Failed to generate direct summary: ${error.message || 'Unknown error'}`);
    }
  }
}

// Generate comprehensive summary from English transcript
export async function generateComprehensiveSummary(
  englishTranscript: string,
  language: OutputLanguage,
  style: OutputStyle,
  size: OutputSize = 'auto'
): Promise<{ summary: string; modelUsed: string }> {
  logger.startTimer('generateSummary');
  logger.info(`[STEP 3/5] Generating comprehensive summary in English (will translate to ${language} later) with ${style} style and ${size} size`);

  // Only log transcript length at debug level
  if (logger.shouldLog(LogLevel.DEBUG)) {
    logger.debug(`English transcript length: ${englishTranscript.length} characters`);
  }

  try {
    // Create prompt based on user preferences
    logger.startTimer('createPrompt');

 // Style Descriptions for GPT-4
const styleDescription = {
  insightful: `- Professional and insightful tone suitable for a business or intellectual audience
- Use thoughtful and analytical emojis (🧠, 💡, 🔍, 🤔, 📊, 🧩, etc.)
- Emphasize deeper meanings, patterns, and connections between concepts
- Highlight intellectual frameworks, mental models, and underlying principles
- Structure content for maximum depth and understanding
- Focus on "why" questions and root causes rather than just surface observations
- Connect ideas to broader contexts and implications
- Include thought-provoking questions or considerations where appropriate`,
  eli5: `- Use extremely simple and clear language that anyone can understand
- Avoid technical jargon, complex terms, and difficult vocabulary
- Break down complicated concepts into basic, easy-to-follow explanations
- Use everyday analogies and simple comparisons when helpful
- Keep sentences short and straightforward
- Explain "why" and "how" things work in the simplest way possible
- Use minimal, relevant emojis only when they add clarity
- Focus on making complex ideas accessible without losing important details
- Ensure comprehensive coverage while maintaining simplicity`,
  funny: `- Light-hearted and humorous tone with a conversational feel
- Use fun and playful emojis (😂, 🤣, 😅, 🙃, 🤪, 🎭, etc.)
- Emphasize amusing observations, ironies, and entertaining aspects
- Include witty remarks, clever wordplay, and humorous analogies
- Structure content for maximum enjoyment and shareability
- Use casual language and conversational phrases
- Incorporate appropriate humor while still conveying all key information
- Balance humor with informative content - don't sacrifice substance for jokes`,
  actionable: `- Practical and solution-oriented tone focused on implementation
- Use action-oriented emojis (✅, 🎯, 🚀, 💪, 🔧, 📝, etc.)
- Emphasize specific steps, methods, techniques, and applications
- Highlight clear opportunities for implementation and practical takeaways
- Structure content for maximum utility and immediate application
- Use imperative verbs and direct instructions where appropriate
- Break down complex processes into manageable steps
- Focus on "how-to" aspects and practical applications of concepts
- Include specific, concrete examples of implementation`,
  controversial: `- Balanced presentation of different perspectives with a neutral tone
- Use thought-provoking emojis (⚖️, 🔄, 🤝, 🧐, 💭, ❓, etc.)
- Emphasize points of debate, tensions, and different viewpoints
- Highlight areas of disagreement, competing ideas, and alternative interpretations
- Structure content to show multiple sides of complex issues
- Present contrasting viewpoints with equal depth and fairness
- Avoid taking sides while still providing comprehensive analysis
- Acknowledge the nuances and complexities of debated topics
- Include the strongest arguments from each perspective`,
  educational: `- Clear, structured, and informative tone optimized for learning
- Use educational and explanatory emojis (📚, 🎓, ✏️, 📝, 🔬, 📊, etc.)
- Emphasize learning points, key concepts, and foundational principles
- Highlight definitions, classifications, and hierarchical relationships
- Structure content for progressive understanding and knowledge building
- Present information in a logical sequence that builds understanding
- Define specialized terminology clearly before using it
- Use examples and analogies to illustrate complex concepts
- Include summative statements that reinforce key learning points`,
  technical: `- Technical and precise tone with specialized vocabulary
- Use industry-specific and technical emojis (⚙️, 💻, 🔧, 📊, 🧮, 🔬, etc.)
- Emphasize specifications, methodologies, technical details, and processes
- Maintain precise terminology, technical accuracy, and domain-specific language
- Structure for maximum technical value and specialized understanding
- Include relevant technical parameters, measurements, and specifications
- Preserve technical nuances and distinctions between similar concepts
- Use appropriate technical frameworks and models from the domain
- Balance technical depth with clarity for the intended audience`,
};

// Simpler descriptions for GPT-3.5
const simpleStyleDescription = {
  insightful: `- Use an intellectual tone and thoughtful emojis
- Focus on deeper ideas, connections, and why things matter
- Highlight patterns, principles, and broader meaning
- Ask smart questions and reflect on big-picture thinking`,
  eli5: `- Use very simple and clear language that everyone can understand
- Avoid complex words and technical terms
- Break down difficult concepts into easy explanations
- Use simple examples and analogies when needed`,
  funny: `- Use a casual and fun tone with playful emojis
- Add jokes, wordplay, and light commentary
- Make it enjoyable and entertaining without losing the message`,
  actionable: `- Use clear steps and practical suggestions
- Include action-based emojis and tips
- Help users apply the content with useful takeaways`,
  controversial: `- Show both sides of any issue fairly
- Use balanced language and thought-provoking emojis
- Explain different views and their reasoning`,
  educational: `- Use a teaching tone and clear examples
- Add learning emojis and build understanding step by step
- Explain new terms and reinforce main ideas`,
  technical: `- Use clear and accurate technical terms
- Add relevant emojis and focus on how things work
- Stick to the facts and include detailed explanations`,
};

// Length modifiers
const sizeModifiers = {
  concise: `- Keep the summary brief and focused on only the most essential points
- Aim for approximately 30-40% shorter than a standard summary
- Prioritize the most impactful information
- Limit the NARRATIVE OVERVIEW to 1 paragraph maximum
- Include 5-8 HIGHLIGHTS instead of the standard number
- Include 3-5 KEY INSIGHTS instead of the standard number
- Be selective about which points to include, focusing on the highest-value information
- Omit secondary details while preserving all critical information
- Use concise language and avoid unnecessary elaboration`,
  detailed: `- Create a comprehensive summary that covers all significant details
- Include more examples and supporting information
- Aim for approximately 30-40% longer than a standard summary
- Expand the NARRATIVE OVERVIEW to 2-3 paragraphs for thorough context
- Include 10-15 HIGHLIGHTS to ensure comprehensive coverage
- Include 6-9 KEY INSIGHTS for deeper analysis
- Include secondary details that add valuable context or nuance
- Provide more examples and evidence to support main points
- Explore implications and connections more thoroughly
- Ensure no significant information from the transcript is omitted`,
  auto: `- Automatically adjust the length based on content complexity and importance
- Use your judgment to determine the appropriate level of detail
- Balance brevity with comprehensiveness based on content importance
- For complex or information-dense content, provide more detail
- For simpler or more straightforward content, be more concise
- Adjust the number of HIGHLIGHTS and KEY INSIGHTS based on content richness
- Prioritize clarity and value over arbitrary length constraints
- Ensure the summary length is proportional to the information density of the original
- Focus on providing maximum value to the reader regardless of length`,
};

// Valid keys for lookup
const styleOptions = Object.keys(styleDescription);
const sizeOptions = Object.keys(sizeModifiers);

// Detect if GPT-4 model (including GPT-4o-mini)
const isGpt4oModel = SELECTED_MODEL.includes('gpt-4o');

// Helper to get style safely
const getStyleDescription = (style: string) =>
  (isGpt4oModel ? styleDescription : simpleStyleDescription as any)[styleOptions.includes(style) ? style : 'educational'];

// Helper to get size modifier safely
const getSizeModifier = (size: string) =>
  (sizeModifiers as any)[sizeOptions.includes(size) ? size : 'auto'];

// Selected modifiers
const selectedStyleDescription = getStyleDescription(style);
const selectedSizeModifier = getSizeModifier(size);

// Prompts

const gpt4Prompt = `
You are an elite content strategist and summarization expert, trained to extract **maximum educational value** from long-form YouTube video transcripts. You write summaries that feel **handcrafted by domain experts**, not by generic AI.

🎯 OBJECTIVE:
Generate an **exceptionally detailed, highly readable, and fully self-contained summary** of the **YouTube video transcript** provided below. Your summary should be better than NoteGPT, ChatGPT, or any automated summary tool — in **quality, insight, and completeness**.

📦 OUTPUT STRUCTURE:
Use these exact headings and formats to ensure structure, scannability, and clarity:

---

1. ✨ HIGHLIGHTS (10–20 points)
   - Begin each point with a **relevant emoji only** (no dashes or bullets)
   - Every point must be **standalone**, clear without needing extra context
   - Include **detailed facts, named individuals, key stats, and exact concepts**
   - Each point should feel like a “quotable insight” or a “knowledge nugget”
   - Avoid vague phrases — **make every word count**
   - Cover the **entire length of the video**, not just the beginning

---

2. 🧠 KEY INSIGHTS (10–20 points)
   - Start each point with an emoji
   - Extract **big-picture ideas**, recurring patterns, and **universal principles**
   - Include **mental models**, cause-effect relationships, and strategic takeaways
   - Help the reader connect content to **real-world relevance or application**
   - Show **why** certain things matter — not just what was said

---

3. 📝 TL;DR SUMMARY (1–2 sentences)
   - Craft a tight, high-clarity overview of the video’s **core message**
   - Include key context and conclusions in a clear, fluid sentence

---

4. 📚 NARRATIVE OVERVIEW (2–4 short paragraphs)
   - Retell the **video's full arc** from intro to conclusion
   - Maintain an **educational tone**; echo the original speaker’s intent and voice
   - Summarize subtopics in **chronological or logical flow**
   - Provide missing background context if needed
   - This section should feel like a **mini article** or an “executive digest”

---

🎨 STYLE GUIDELINES:
- Use spaced-out blocks for **easy skimming**
- **Avoid nested bullets or in-paragraph lists**
- Match the video’s tone — technical, conversational, motivational, etc.
- Every point must be **independently valuable** to the reader
- Assume the reader has **no access to the video** — all context must be included

📏 LENGTH + DEPTH:
${selectedSizeModifier}

🧠 STYLE & TONE:
${selectedStyleDescription}

✅ QUALITY CHECKLIST:
- No made-up content — rely **strictly on the transcript**
- Capture **everything meaningful**, even subtle points or minor arguments
- Include **specific numbers, named people, events, quotes, or locations**
- Ensure every bullet, insight, and paragraph is **dense with real value**

🌍 LANGUAGE:
Write in **English** — with professional fluency and clarity

---

🎥 TRANSCRIPT INPUT:
"""
${englishTranscript}
"""
`;



const gpt35Prompt = `
You are an advanced summarization assistant trained to generate **expert-level, information-dense summaries** from long YouTube transcripts. Your goal is to create a **complete, structured, and highly detailed summary** that matches or exceeds what a human expert or editor would produce.

🎯 OBJECTIVE:
Produce a professional-quality, educational summary in English that covers **every important point, argument, example, and fact** in the transcript. The output should feel **expert-curated**, not machine-generated. Your summary will be used by professionals, educators, and learners.

---

# 📌 STRUCTURE & FORMATTING (follow exactly)

### 1. #**Highlights**

- Provide **10 to 20 detailed bullet points**
- Start every point with a **relevant emoji** (⚠️ 💡 🧠 🔍 📊 etc.) + **1 space**
- Include **names, facts, numbers, stats, quotes, or examples**
- Each point should be **self-contained and complete** — no vague summaries
- Cover **the entire video** evenly (not just the beginning)
- These are the "core facts and moments" from the video

---

### 2. #**Key Insights**

- Provide **8 to 20 deeper insight bullets**
- Start each line with an emoji + bolded takeaway title (e.g. 🧠 **Cognitive Bias:**...)
- These should explain **underlying patterns, implications, or strategies**
- Include **cause-effect relationships**, big-picture thinking, and recurring themes
- Relate ideas to real-world relevance where possible (social, historical, or technical context)

---

### 3. #**TL;DR Summary**

- Write **1 to 2 full sentences** summarizing the **core essence** of the video
- Be specific, **use named concepts or arguments**, not generalities
- This section should read like a strong "editorial lead" or meta-summary

---

### 4. #**Narrative Overview**

- Write **2 to 4 short paragraphs** that summarize the **flow of the video**
- Follow the **chronological or thematic structure** of how the content was presented
- Use a clean, educational tone
- Include background info or context if necessary
- Mention important transitions or topic shifts

---

# 📏 DEPTH & COMPLETENESS

${selectedSizeModifier}

# 🧠 OUTPUT STYLE

${selectedStyleDescription}

---

# ⚠️ CRITICAL RULES

- DO NOT invent or guess any information
- DO NOT use vague filler like "The video explains…" or "It talks about..."
- Every point must include **specific content** from the transcript
- Keep **sections clearly separated** — never merge or rename sections
- Use **clean layout**, spacing, and scannable formatting

---

# 🌍 LANGUAGE: English only

---

# 🎥 TRANSCRIPT:

"""
${englishTranscript}
"""
`;





    // Select the appropriate prompt based on the model
    const prompt = isGpt4oModel ? gpt4Prompt : gpt35Prompt;
    logger.endTimer('createPrompt');

    // Only log prompt details at debug level
    logger.debug(() => 'Prompt details:', {
      promptLength: prompt.length,
      language,
      style
    });

    // Use the selected model
    const modelToUse = SELECTED_MODEL;

    // Call OpenAI API
    logger.info(`Calling OpenAI Chat Completions API with ${modelToUse} model`);
    logger.startTimer('openaiGptCall');

    const completion = await openai.chat.completions.create({
      messages: [{ role: 'user', content: prompt }],
      model: modelToUse,
    });

    logger.endTimer('openaiGptCall', LogLevel.INFO);

    if (completion.choices && completion.choices.length > 0 && completion.choices[0].message.content) {
      const post = completion.choices[0].message.content;
      logger.info(`Summary generated successfully (${post.length} characters)`);

      // Only log preview at debug level
      if (logger.shouldLog(LogLevel.DEBUG)) {
        const preview = post.substring(0, 100) + (post.length > 100 ? '...' : '');
        logger.debug('Summary preview:', { preview });
      }

      // Log usage information only at debug level
      if (completion.usage) {
        logger.debug('API usage stats:', {
          promptTokens: completion.usage.prompt_tokens,
          completionTokens: completion.usage.completion_tokens,
          totalTokens: completion.usage.total_tokens
        });
      }

      // Post-processing verification step to ensure comprehensive coverage
      logger.startTimer('verifyComprehensiveness');
      logger.info('Performing post-processing verification to ensure comprehensive coverage');

      // Create a variable to hold the final summary that we can modify
      const finalSummary = post;

      try {
        // Check if the summary is comprehensive enough by comparing with the transcript
        // We'll use a simple heuristic based on the ratio of summary length to transcript length
        const summaryLength = post.length;
        const transcriptLength = englishTranscript.length;
        const ratio = summaryLength / transcriptLength;

        logger.debug(`Summary verification - Summary length: ${summaryLength}, Transcript length: ${transcriptLength}, Ratio: ${ratio.toFixed(2)}`);

        // If the summary is too short relative to the transcript, it might be missing details
        // This is a simple heuristic and can be adjusted based on empirical results
        const minRatio = size === 'concise' ? 0.15 : size === 'detailed' ? 0.3 : 0.2;

        if (ratio < minRatio && transcriptLength > 1000) {
          logger.warn(`Summary may be missing details - ratio (${ratio.toFixed(2)}) is below threshold (${minRatio})`);
          // Log the verification result but don't modify the summary
          logger.info('Summary verification: Below threshold but continuing without adding note');
        } else {
          logger.info(`Summary verification passed - ratio (${ratio.toFixed(2)}) is above threshold (${minRatio})`);
        }
      } catch (verificationError: any) {
        // If verification fails, log it but don't fail the whole process
        logger.warn(`Summary verification failed: ${verificationError?.message || 'Unknown error'}`);
      }

      logger.endTimer('verifyComprehensiveness');
      logger.endTimer('generateSummary', LogLevel.INFO);

      return {
        summary: finalSummary.replace(/^\s*[-•]\s+/gm, ''),
        modelUsed: modelToUse
      };
    } else {
      logger.error('Empty response from OpenAI API');
      throw new Error('Failed to generate summary: Empty response from OpenAI');
    }
  } catch (error: any) {
    // Log basic error at ERROR level
    logger.error(`Error generating summary: ${error.message}`);

    // Log detailed error info at DEBUG level
    logger.debug('Summary generation error details:', {
      error: error.message,
      code: error.code,
      status: error.status
    });

    // Provide a more user-friendly error message
    if (error.code === 'ECONNRESET') {
      throw new Error('Connection to OpenAI was reset. Please check your internet connection and try again.');
    } else {
      throw new Error(`Failed to generate summary: ${error.message || 'Unknown error'}`);
    }
  }
}

// Define log entry type
export type LogEntry = {
  timestamp: string;
  level: string;
  message: string;
};

// Main function to process YouTube video and generate comprehensive summary
export async function processYouTubeVideo(
  videoUrl: string,
  language: OutputLanguage,
  style: OutputStyle,
  size: OutputSize = 'auto',
  useAudioTranscription: boolean = false // Parameter to control whether to use audio transcription
): Promise<{
  post: string,
  transcript: string,
  transcriptSource: 'youtube-captions' | 'audio-transcription',
  logs: LogEntry[],
  modelUsed: string,
  videoTitle?: string,
  videoDuration?: string,
  videoPublishDate?: string
}> {
  // Array to collect logs
  const logs: LogEntry[] = [];

  // Helper function to log and capture the log entry without duplicate logging
  const log = (level: string, message: string) => {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0]; // HH:MM:SS format
    logs.push({ timestamp, level, message });

    // Skip console logging in production to improve performance
    if (logger.isProduction) return;

    // We don't call the logger methods to avoid duplicate logging
    // The logs array will be returned to the client for display
  };

  log('INFO', `[STEP 0/4] Starting YouTube video processing for comprehensive summary`);
  log('INFO', `Processing request: URL=${videoUrl}, Language=${language}, Summary Style=${style}, Size=${size}`);

  const startTime = Date.now();

  try {
    // Validate API key before proceeding
    if (!env.OPENAI_API_KEY) {
      log('ERROR', 'OpenAI API key is not set');
      throw new Error('Please set a valid OpenAI API key in your .env.local file');
    }

    // First, try to fetch YouTube transcript directly
    let transcript = null;
    let audioPath = null;
    let transcriptSource: 'youtube-captions' | 'audio-transcription' = 'youtube-captions'; // Default to YouTube captions

    try {
      // Attempt to get transcript directly from YouTube
      log('INFO', 'Attempting to fetch YouTube captions/transcript...');
      transcript = await fetchYouTubeTranscript(videoUrl);
      log("INFO",transcript)
      if (transcript) {
        log('INFO', '✅ Successfully fetched YouTube captions/transcript');
        transcriptSource = 'youtube-captions';
        log('INFO', `📊 TRANSCRIPT SOURCE: YOUTUBE CAPTIONS`);
      } else if (useAudioTranscription) {
        // If transcript fetching failed and audio transcription is enabled, fall back to audio transcription
        log('INFO', '❌ No YouTube captions/transcript available, falling back to audio transcription');
        log('INFO', `Audio transcription is enabled (useAudioTranscription=${useAudioTranscription})`);

        // Download audio from YouTube video
        audioPath = await downloadYouTubeAudio(videoUrl);

        // Log language for debugging
        log('INFO', `Transcribing audio in language: ${language}`);

        // Transcribe audio
        transcript = await transcribeAudio(audioPath, language);
        transcriptSource = 'audio-transcription';
        log('INFO', `📊 TRANSCRIPT SOURCE: AUDIO TRANSCRIPTION`);
      } else {
        // If transcript fetching failed and audio transcription is disabled, throw an error
        log('ERROR', '❌ No YouTube captions/transcript available and audio transcription is disabled');
        throw new Error('No YouTube captions/transcript available for this video. Audio transcription is disabled. Please try a different video that has captions enabled.');
      }
    } catch (transcriptError: any) {
      log('ERROR', `Error during transcript acquisition: ${transcriptError.message}`);

      // If transcript fetching failed but we haven't tried audio yet, try audio transcription
      if (!audioPath) {
        log('INFO', '❌ Captions/transcript fetching failed, falling back to audio transcription');

        // Download audio from YouTube video
        audioPath = await downloadYouTubeAudio(videoUrl);

        // Log language for debugging
        log('INFO', `Transcribing audio in language: ${language}`);

        // Transcribe audio
        transcript = await transcribeAudio(audioPath, language);
        transcriptSource = 'audio-transcription';
        log('INFO', `📊 TRANSCRIPT SOURCE: AUDIO TRANSCRIPTION`);
      } else {
        // Both methods failed
        throw transcriptError;
      }
    }

    let finalSummary: string;
    let modelUsed: string;
    let englishTranscript: string = transcript; // Default to original transcript
    let summaryModelUsed: string = '';
    let englishSummary: string = '';
    let translationModelUsed: string = '';

    if (multiStepPipeline) {
      // Multi-step pipeline: translate to English → summarize → translate to target
      log('INFO', `[MULTI-STEP] Using multi-step pipeline: translate to English → summarize → translate to ${language}`);

      // Step 1: Translate transcript to English (if not already in English)
      log('INFO', `[STEP 2/5] Translating transcript to English...`);
      const translationResult = await translateToEnglish(transcript);
      englishTranscript = translationResult.translatedText;
      translationModelUsed = translationResult.modelUsed;
      log('INFO', 'Transcript translation to English completed successfully');
      log('INFO', `Model used for translation: ${translationModelUsed}`);

      // Step 2: Generate comprehensive summary in English
      log('INFO', `[STEP 3/5] Generating comprehensive summary in English with ${style} style and ${size} size...`);
      const summaryResult = await generateComprehensiveSummary(englishTranscript, language, style, size);
      englishSummary = summaryResult.summary;
      summaryModelUsed = summaryResult.modelUsed;
      log('INFO', 'English summary generation completed successfully');
      log('INFO', `Model used for summary: ${summaryModelUsed}`);

      // Step 3: Translate summary to target language (if not English)
      log('INFO', `[STEP 4/5] Translating summary to ${language}...`);
      const finalTranslationResult = await translateToTargetLanguage(englishSummary, language);
      finalSummary = finalTranslationResult.translatedText;
      const finalTranslationModelUsed = finalTranslationResult.modelUsed;
      log('INFO', 'Summary translation to target language completed successfully');
      log('INFO', `Model used for final translation: ${finalTranslationModelUsed}`);

      // Combine model information for reporting
      const allModelsUsed = [translationModelUsed, summaryModelUsed, finalTranslationModelUsed]
        .filter(model => model !== 'no-translation-needed')
        .join(', ');
      modelUsed = allModelsUsed || summaryModelUsed;
    } else {
      // Single-step pipeline: directly generate summary in target language
      log('INFO', `[SINGLE-STEP] Using single-step pipeline: directly generating summary in ${language}`);
      log('INFO', `[STEP 2/3] Generating comprehensive summary directly in ${language} with ${style} style and ${size} size...`);

      const { summary: directSummary, modelUsed: directModelUsed } = await generateDirectSummary(transcript, language, style, size);
      finalSummary = directSummary;
      modelUsed = directModelUsed;
      summaryModelUsed = directModelUsed; // For consistency in retry logic

      log('INFO', 'Direct summary generation completed successfully');
      log('INFO', `Model used for direct summary: ${directModelUsed}`);
    }

    // Check if the summary might be missing important details
    // We'll use a simple heuristic based on the ratio of summary length to transcript length
    const summaryLength = finalSummary.length;
    const transcriptLength = englishTranscript.length;
    const ratio = summaryLength / transcriptLength;

    // Define minimum ratio thresholds based on size parameter
    const minRatio = size === 'concise' ? 0.15 : size === 'detailed' ? 0.3 : 0.2;

    // If the summary is too short relative to the transcript and we're using GPT-3.5, try again with more explicit instructions
    if (ratio < minRatio && transcriptLength > 1000 && summaryModelUsed.includes('gpt-3.5') && !summaryModelUsed.includes('gpt-4')) {
      log('WARN', `Summary may be missing details - ratio (${ratio.toFixed(2)}) is below threshold (${minRatio}). Attempting to generate a more comprehensive summary...`);

      try {
        if (multiStepPipeline) {
          // Multi-step pipeline retry
          const { summary: improvedEnglishSummary, modelUsed: modelUsedForImprovedSummary } = await generateComprehensiveSummary(
            englishTranscript,
            language,
            style,
            'detailed' // Force detailed size for the second attempt
          );

          // If the improved summary is significantly longer, use it instead
          if (improvedEnglishSummary.length > englishSummary.length * 1.3) {
            log('INFO', `Generated a more comprehensive English summary (${improvedEnglishSummary.length} chars vs original ${englishSummary.length} chars)`);

            // Translate the improved summary to target language
            const { translatedText: improvedFinalSummary, modelUsed: improvedTranslationModelUsed } = await translateToTargetLanguage(improvedEnglishSummary, language);

            finalSummary = improvedFinalSummary;
            modelUsed = [translationModelUsed, modelUsedForImprovedSummary, improvedTranslationModelUsed]
              .filter(model => model !== 'no-translation-needed')
              .join(', ') || modelUsedForImprovedSummary;
          } else {
            log('INFO', `Second summary attempt did not produce significantly more comprehensive results. Keeping original summary.`);
          }
        } else {
          // Single-step pipeline retry
          const { summary: improvedDirectSummary, modelUsed: modelUsedForImprovedSummary } = await generateDirectSummary(
            transcript,
            language,
            style,
            'detailed' // Force detailed size for the second attempt
          );

          // If the improved summary is significantly longer, use it instead
          if (improvedDirectSummary.length > finalSummary.length * 1.3) {
            log('INFO', `Generated a more comprehensive direct summary (${improvedDirectSummary.length} chars vs original ${finalSummary.length} chars)`);
            finalSummary = improvedDirectSummary;
            modelUsed = modelUsedForImprovedSummary;
          } else {
            log('INFO', `Second summary attempt did not produce significantly more comprehensive results. Keeping original summary.`);
          }
        }
      } catch (retryError: any) {
        // If the retry fails, log it but keep the original summary
        log('WARN', `Failed to generate improved summary: ${retryError?.message || 'Unknown error'}. Keeping original summary.`);
      }
    }

    // Clean up the audio file if it was created
    if (audioPath) {
      try {
        const fs = require('fs');
        if (fs.existsSync(audioPath)) {
          fs.unlinkSync(audioPath);
          log('INFO', `[STEP 4/4] Temporary audio file deleted: ${audioPath}`);
        }
      } catch (cleanupError: any) {
        log('WARN', `Error cleaning up audio file: ${cleanupError.message}`);
        // Don't throw here, as the main functionality succeeded
      }
    } else {
      log('INFO', '[STEP 5/5] No audio file to clean up (used YouTube transcript)');
    }

    const totalDuration = ((Date.now() - startTime) / 1000).toFixed(2);
    log('INFO', `YouTube processing completed successfully in ${totalDuration}s`);
    log('INFO', `Final summary length: ${finalSummary.length} characters`);
    log('INFO', `Original transcript length: ${transcript.length} characters`);
    if (multiStepPipeline) {
      log('INFO', `English transcript length: ${englishTranscript.length} characters`);
    }
    log('INFO', `Pipeline mode: ${multiStepPipeline ? 'Multi-step' : 'Single-step'}`);
    log('INFO', `Transcript source: ${transcriptSource}`);
    log('INFO', `Models used: ${modelUsed}`);

    // Extract video metadata from info if available
    let videoTitle: string | undefined = undefined;
    let videoDuration: string | undefined = undefined;
    let videoPublishDate: string | undefined = undefined;

    try {
      // Always try to get the video metadata, regardless of whether we used audio transcription or captions
      const info = await ytdl.getInfo(videoUrl);
      videoTitle = info.videoDetails.title;

      // Format duration from seconds to MM:SS or HH:MM:SS
      const lengthSeconds = parseInt(info.videoDetails.lengthSeconds);
      if (lengthSeconds) {
        const hours = Math.floor(lengthSeconds / 3600);
        const minutes = Math.floor((lengthSeconds % 3600) / 60);
        const seconds = lengthSeconds % 60;

        if (hours > 0) {
          videoDuration = `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        } else {
          videoDuration = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        }
      }

      // Format publish date
      if (info.videoDetails.publishDate) {
        const publishDate = new Date(info.videoDetails.publishDate);
        videoPublishDate = publishDate.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });
      }

      log('INFO', `Video metadata extracted: ${videoTitle}`);
      if (videoDuration) log('INFO', `Video duration: ${videoDuration}`);
      if (videoPublishDate) log('INFO', `Video publish date: ${videoPublishDate}`);
    } catch (metadataError: any) {
      log('WARN', `Could not extract video metadata: ${metadataError?.message || 'Unknown error'}`);
    }

    return {
      post: finalSummary,
      transcript,
      transcriptSource,
      logs,
      modelUsed,
      videoTitle,
      videoDuration,
      videoPublishDate
    };
  } catch (error: any) {
    const failureDuration = ((Date.now() - startTime) / 1000).toFixed(2);
    log('ERROR', `Processing failed after ${failureDuration}s: ${error.message}`);

    // Provide more specific error messages based on the error type
    if (error.message?.includes('API key')) {
      log('ERROR', 'API key configuration issue detected');
      throw new Error('OpenAI API key issue: ' + error.message);
    } else if (error.message?.includes('Invalid YouTube URL')) {
      log('ERROR', `Invalid YouTube URL provided: ${videoUrl}`);
      throw new Error('Please provide a valid YouTube URL');
    } else if (error.code === 'ECONNRESET' || error.message?.includes('ECONNRESET')) {
      log('ERROR', 'Connection reset error detected');
      throw new Error('Connection to OpenAI was reset. Please check your internet connection and try again.');
    } else if (error.message?.includes('403') || error.message?.includes('Forbidden')) {
      log('ERROR', 'OpenAI API access forbidden (403 error)');
      throw new Error('OpenAI API access forbidden (403 error). This usually means your OpenAI account does not have billing set up, which is required for using the Whisper API, or your API key is invalid. Please check your OpenAI account settings.');
    } else {
      log('ERROR', `Unexpected error during processing: ${error.message}`);
      throw new Error(error.message || 'An unexpected error occurred while processing the video');
    }
  }
}
