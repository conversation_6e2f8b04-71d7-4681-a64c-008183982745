'use client';

import React from 'react';

export default function EmptySummaryState() {
  return (
    <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-8 flex flex-col items-center justify-center min-h-[400px] border border-gray-200 dark:border-gray-700">
      <div className="mb-6">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="80"
          height="80"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="1"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="text-gray-300 dark:text-gray-600"
        >
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
          <path d="M14 2v6h6" />
          <path d="M16 13H8" />
          <path d="M16 17H8" />
          <path d="M10 9H8" />
        </svg>
      </div>

      <h3 className="text-xl font-medium text-gray-700 dark:text-gray-300 mb-2">
        Ready to Generate a Summary
      </h3>

      <p className="text-gray-500 dark:text-gray-400 text-center mb-6 max-w-md">
        Enter a YouTube URL in the search box above to create a comprehensive,
        intelligent summary of the video content.
      </p>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 w-full max-w-lg">
        <div className="bg-white dark:bg-gray-600 p-4 rounded-lg border border-gray-200 dark:border-gray-500 flex flex-col items-center">
          <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mb-2">
            <span className="text-blue-600 dark:text-blue-300 text-sm font-bold">1</span>
          </div>
          <p className="text-xs text-center text-gray-600 dark:text-gray-200">Paste YouTube URL</p>
        </div>

        <div className="bg-white dark:bg-gray-600 p-4 rounded-lg border border-gray-200 dark:border-gray-500 flex flex-col items-center">
          <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mb-2">
            <span className="text-blue-600 dark:text-blue-300 text-sm font-bold">2</span>
          </div>
          <p className="text-xs text-center text-gray-600 dark:text-gray-200">Customize Options</p>
        </div>

        <div className="bg-white dark:bg-gray-600 p-4 rounded-lg border border-gray-200 dark:border-gray-500 flex flex-col items-center">
          <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mb-2">
            <span className="text-blue-600 dark:text-blue-300 text-sm font-bold">3</span>
          </div>
          <p className="text-xs text-center text-gray-600 dark:text-gray-200">Get Smart Summary</p>
        </div>
      </div>
    </div>
  );
}
