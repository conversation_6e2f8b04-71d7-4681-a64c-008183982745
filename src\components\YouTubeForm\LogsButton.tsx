'use client';

import { But<PERSON> } from '@/components/ui/button';
import { useFormContext } from './FormContext';

export default function LogsButton() {
  const { logs, showLogs, setShowLogs } = useFormContext();

  // Always show the logs button, but disable it if there are no logs
  const hasLogs = logs.length > 0;

  return (
    <div className="flex justify-between items-center border-b pb-2">
      <h2 className="text-lg font-semibold">Processing Logs</h2>
      <Button
        onClick={() => setShowLogs(!showLogs)}
        variant="ghost"
        size="sm"
        disabled={!hasLogs}
        className={`text-xs flex items-center gap-1 ${!hasLogs ? 'opacity-50 cursor-not-allowed' : ''}`}
      >
        {showLogs ? (
          <>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="m18 15-6-6-6 6"/>
            </svg>
            Hide Logs
          </>
        ) : (
          <>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="m6 9 6 6 6-6"/>
            </svg>
            Show Logs
          </>
        )}
      </Button>
    </div>
  );
}
