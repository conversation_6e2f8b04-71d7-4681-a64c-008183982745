'use client';

import React from 'react';

interface LogEntry {
  timestamp: string;
  level: string;
  message: string;
}

interface LogDisplayProps {
  logs: LogEntry[];
  visible: boolean;
}

export default function LogDisplay({ logs, visible }: LogDisplayProps) {
  if (!visible || !logs?.length) return null;

  // Helper function to format log messages with pipeline indicators
  const formatLogMessage = (message: string) => {
    // Highlight pipeline mode indicators
    if (message.includes('[MULTI-STEP]')) {
      return message.replace('[MULTI-STEP]', '🔄 [MULTI-STEP]');
    }
    if (message.includes('[SINGLE-STEP]')) {
      return message.replace('[SINGLE-STEP]', '⚡ [SINGLE-STEP]');
    }
    if (message.includes('[TRANSLATION]')) {
      return message.replace('[TRANSLATION]', '🌐 [TRANSLATION]');
    }
    if (message.includes('Pipeline mode:')) {
      return message.replace('Pipeline mode:', '🔧 Pipeline mode:');
    }
    return message;
  };

  return (
    <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-md text-xs font-mono overflow-auto max-h-[200px] shadow-sm mt-2">
      <div className="space-y-1">
        {logs.map((log, index) => (
          <div
            key={index}
            className={`
              ${log.level === 'ERROR' ? 'text-red-600 dark:text-red-400' : ''}
              ${log.level === 'WARN' ? 'text-yellow-600 dark:text-yellow-400' : ''}
              ${log.level === 'INFO' ? 'text-blue-600 dark:text-blue-400' : ''}
              ${log.level === 'DEBUG' ? 'text-gray-600 dark:text-gray-400' : ''}
            `}
          >
            <span className="text-gray-500 opacity-70">[{log.timestamp}]</span>{' '}
            <span className="font-semibold">[{log.level}]</span>{' '}
            <span className="whitespace-pre-wrap">{formatLogMessage(log.message)}</span>
          </div>
        ))}
      </div>
    </div>
  );
}
