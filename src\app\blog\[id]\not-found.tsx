import Link from 'next/link';
import { ArrowRight } from 'lucide-react';

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gray-900 text-white flex items-center justify-center">
      <div className="text-center max-w-md mx-auto px-6">
        <div className="mb-8">
          <div className="w-24 h-24 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-6">
            <span className="text-4xl">📝</span>
          </div>
          <h1 className="text-3xl font-bold mb-4 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
            Blog Post Not Found
          </h1>
          <p className="text-gray-400 mb-8">
            The blog post you&apos;re looking for doesn&apos;t exist or may have been moved.
          </p>
        </div>
        
        <div className="space-y-4">
          <Link 
            href="/blog" 
            className="inline-flex items-center bg-gradient-to-r from-purple-500 to-pink-500 px-6 py-3 rounded-lg font-medium hover:from-purple-600 hover:to-pink-600 transition-all"
          >
            <ArrowRight className="w-4 h-4 mr-2 rotate-180" />
            Back to Blog
          </Link>
          
          <div className="text-sm text-gray-500">
            <Link href="/" className="text-purple-400 hover:text-purple-300 transition-colors">
              Or go to homepage
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
