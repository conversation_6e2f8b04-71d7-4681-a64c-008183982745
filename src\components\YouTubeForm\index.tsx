'use client';

import React from 'react';
import { FormProvider, useFormContext } from './FormContext';
import YouTubeUrlInput from './YouTubeUrlInput';
// import ModelSelector from './ModelSelector';
import ErrorDisplay from './ErrorDisplay';
// import LogsButton from './LogsButton';
import LogDisplay from '../LogDisplay';
import VideoPreview from './VideoPreview';
import VideoThumbnails, { dummyThumbnails } from './VideoThumbnails';
import TabsContainer from './TabsContainer';
// import InfoSection from './InfoSection';

// Form component that uses the context
function YouTubeFormContent() {
  const { 
    handleSubmit, 
    logs, 
    showLogs, 
    setVideoTitle, 
    setVideoThumbnail,
    setVideoId,
    setPost,
    videoId
  } = useFormContext();

  const handleThumbnailClick = (data: {
    title: string;
    summary: string;
    thumbnail: string;
    videoId: string;
  }) => {
    setVideoTitle(data.title);
    setVideoThumbnail(data.thumbnail);
    setVideoId(data.videoId);
    setPost(data.summary);
    // Don't update the URL input field when clicking thumbnails
    // setVideoUrl(`https://youtu.be/${data.videoId}`);
  };

  // Set default video preview on component mount if no video is selected
  React.useEffect(() => {
    if (!videoId) {
      setVideoTitle(dummyThumbnails[0].title);
      setVideoThumbnail(dummyThumbnails[0].url);
      setVideoId(dummyThumbnails[0].id.toString());
      setPost(dummyThumbnails[0].summary);
    }
  }, [setVideoTitle, setVideoThumbnail, setVideoId, setPost, videoId]);

  return (
    <div className="w-full max-w-[1200px] mx-auto p-6 bg-background rounded-2xl border border-gray-200 dark:border-gray-700">
      {/* <h1 className="text-2xl font-bold mb-6 text-center">
        YouTube Video Smart Summary Generator
      </h1> */}

      {/* URL Input Bar with separate submit button */}
      <div className="mb-4 mx-auto">
        <form onSubmit={handleSubmit}>
          <YouTubeUrlInput />
        </form>
      </div>

      <ErrorDisplay />

      {/* Two-column layout */}
      <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
        {/* Left column - Form controls and video player */}
        <div className="lg:col-span-6 space-y-6">
          {/* Video Preview/Player - Always present to prevent flickering */}
          <div className="">
            <VideoPreview />
          </div>
          
          {/* Video Thumbnails */}
          <div className="">
            <VideoThumbnails onThumbnailClick={handleThumbnailClick} />
          </div>

          {/* Model selector - Always present */}
          <div className="space-y-6">
            {/* <ModelSelector /> */}
            {/* <InfoSection /> */}
          </div>

          {/* Logs section - Always present but conditionally enabled */}
          {/* <LogsButton /> */}

          {/* Only show logs in left column */}
          <LogDisplay logs={logs} visible={showLogs} />
        </div>

        {/* Right column - Results */}
        <div className="lg:col-span-6">
          <TabsContainer />
        </div>
      </div>

      {/* Transcript is now displayed in the tabs */}
    </div>
  );
}

// Main component that provides the context
export default function YouTubeForm() {
  return (
    <FormProvider>
      <YouTubeFormContent />
    </FormProvider>
  );
}
