'use client';

import { usePathname } from 'next/navigation';
import Mascot from './Mascot';

const AUTH_PAGES = ['/login', '/signup', '/auth'];

export function Footer() {
  const pathname = usePathname();
  const isAuthPage = AUTH_PAGES.some(page => pathname?.startsWith(page));

  if (isAuthPage) return null;

  return (
    <footer className="border-t border-gray-800 mt-20">
      <div className="max-w-7xl mx-auto px-6 py-12">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="flex items-center space-x-2 mb-4 md:mb-0">
            <div className="w-8 h-8 flex items-center justify-center">
              <Mascot size="sm" expression="happy" />
            </div>
            <span className="font-bold text-xl">Nobot</span>
          </div>

          <div className="flex items-center space-x-8 flex-wrap">
            <a
              href="/"
              className="text-gray-400 hover:text-white transition-colors"
            >
              Home
            </a>
            <a
              href="/pricing"
              className="text-gray-400 hover:text-white transition-colors"
            >
              Pricing
            </a>
            <a
              href="#"
              className="text-gray-400 hover:text-white transition-colors"
            >
              Support
            </a>
            <a
              href="/blog"
              className="text-gray-400 hover:text-white transition-colors"
            >
              Blog
            </a>
          </div>
        </div>


        <div className="border-t border-gray-800 mt-8 pt-8 text-center">
          <p className="text-gray-400">
            © 2023 Nobot. All rights reserved.
          </p>
          <div className="flex items-center justify-center space-x-6 mt-4">
            <a
              href="#"
              className="text-gray-400 hover:text-white transition-colors"
            >
              Terms
            </a>
            <a
              href="#"
              className="text-gray-400 hover:text-white transition-colors"
            >
              Privacy
            </a>
            <a
              href="#"
              className="text-gray-400 hover:text-white transition-colors"
            >
              Cookies
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
}
