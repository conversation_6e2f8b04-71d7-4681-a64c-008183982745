'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import MarkdownRenderer from '../MarkdownRenderer';
import { useFormContext } from './FormContext';

export default function TranscriptDisplay() {
  const { transcript, transcriptSource } = useFormContext();

  // State to toggle transcript visibility
  const [isVisible, setIsVisible] = React.useState(false);

  if (!transcript) return null;

  return (
    <div className="w-full mt-8 bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-800">
      <div className="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-800">
        <h2 className="text-xl font-semibold">Transcript</h2>
        <div className="flex items-center gap-2">
          {transcriptSource && (
            <div className={`px-2 py-1 rounded-full text-xs font-medium ${
              transcriptSource === 'youtube-captions'
                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
            }`}>
              {transcriptSource === 'youtube-captions' ? 'YouTube Captions' : 'Audio Transcription'}
            </div>
          )}
          <button
            onClick={() => setIsVisible(!isVisible)}
            className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700"
            aria-label={isVisible ? "Hide transcript" : "Show transcript"}
          >
            {isVisible ? (
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="m18 15-6-6-6 6"/>
              </svg>
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="m6 9 6 6 6-6"/>
              </svg>
            )}
          </button>
        </div>
      </div>

      {isVisible && (
        <div className="p-4">
          <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-md shadow-sm max-h-[400px] overflow-y-auto" style={{
            fontFamily: 'var(--font-inter)',
            fontSize: '16px',
            fontStyle: 'normal',
            fontWeight: 400,
            letterSpacing: '-0.08px',
            lineHeight: '22px'
          }}>
            <MarkdownRenderer content={transcript} />
          </div>

          <div className="flex gap-2 mt-4">
            <Button
              onClick={() => {
                navigator.clipboard.writeText(transcript);
              }}
              variant="outline"
              size="sm"
              className="flex items-center gap-1"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <rect width="14" height="14" x="8" y="8" rx="2" ry="2" />
                <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2" />
              </svg>
              Copy Transcript
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
