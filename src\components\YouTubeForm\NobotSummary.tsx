"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  ThumbsUp,
  ThumbsDown,
  Co<PERSON>,
  Share2,
  Clock,
  DownloadCloud,
  Bookmark,
} from "lucide-react";
import Image from "next/image";

const NobotSummary = () => {
  const theme = "dark";
  const [showSummary, setShowSummary] = useState(true);
  const [summaryLoading, setSummaryLoading] = useState(false);
  const [activeFilter, setActiveFilter] = useState("all");
  const [activeTab, setActiveTab] = useState<"bullet" | "paragraph">("bullet");
  const [readingLevel] = useState<"simple" | "detailed" | "advanced">("detailed");

  const summaryPoints = [
    {
      title: "Childhood and Upbringing",
      points: [
        "<PERSON>'s unique upbringing in apartheid South Africa as a mixed-race child shaped his strong sense of humor and resilience.",
        "In apartheid South Africa, <PERSON>'s mother had to disguise as his caretaker to avoid suspicion due to strict laws preventing interracial relationships.",
        "Growing up in an unsafe household with domestic violence profoundly impacted <PERSON>'s understanding of love and relationships.",
      ],
    },
    {
      title: "Identity and Resilience",
      points: [
        "<PERSON>'s experiences of being different in apartheid South Africa shaped his understanding of identity and resilience.",
        "<PERSON> developed hyper-sensitivity to others' feelings and an acute sense of predicting danger due to his upbringing in an abusive environment.",
        "His ability to speak multiple languages helped him navigate different cultural contexts and connect with diverse groups.",
      ],
    },
  ];

  const paragraphSummary = `<PERSON> shares how his upbringing in apartheid South Africa as a mixed-race child shaped his perspective and sense of humor. He discusses how his mother had to pretend to be his caretaker due to laws against interracial relationships, and how growing up amid domestic violence affected his understanding of relationships. These challenging experiences developed his sensitivity to others' emotions and his ability to predict danger. Noah's multilingual skills helped him navigate diverse cultural contexts, ultimately contributing to his success as a comedian and host. He explains that his decision to leave The Daily Show was driven by a realization about his mental health, connecting symptoms of depression to untreated ADHD, and his desire for a more balanced life that allowed time for personal growth and connection.`;

  return (
    <div className="container relative">
      {/* Summary Section */}
      {showSummary && (
        <div className="max-w-5xl mx-auto mt-2">
          <div className="rounded-xl shadow-xl border border-gray-800 bg-gray-800 p-8">
            <div className="flex items-center gap-2 mb-6">
              <div className="h-8 w-8 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white font-bold text-sm">
                N
              </div>
              <h3 className="font-space font-bold text-xl">Summary</h3>
            </div>

            {summaryLoading ? (
              <div className="space-y-6">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="w-full md:w-1/3 h-40 bg-gray-700 rounded animate-pulse" />
                  <div className="space-y-2 flex-1">
                    <div className="h-6 bg-gray-700 rounded animate-pulse w-3/4" />
                    <div className="h-4 bg-gray-700 rounded animate-pulse w-full" />
                    <div className="h-4 bg-gray-700 rounded animate-pulse w-full" />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="h-6 bg-gray-700 rounded animate-pulse w-1/2" />
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-700 rounded animate-pulse w-full" />
                    <div className="h-4 bg-gray-700 rounded animate-pulse w-full" />
                    <div className="h-4 bg-gray-700 rounded animate-pulse w-full" />
                  </div>
                </div>
              </div>
            ) : (
              <>
                <div className="flex flex-col md:flex-row gap-6 mb-8">
                  <div className="w-full md:w-1/3 rounded-lg overflow-hidden relative group">
                    <Image
                      src="https://i3.ytimg.com/vi/yGLzpt3caXA/maxresdefault.jpg"
                      alt="Trevor Noah: My Depression Was Linked To ADHD! Why I Left The Daily Show!"
                      className="w-full h-auto object-cover transition-transform group-hover:scale-105"
                      width={1280}
                      height={720}
                      priority
                    />
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-14 h-14 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center transition-transform transform group-hover:scale-110">
                        <div className="w-0 h-0 border-t-8 border-t-transparent border-l-12 border-l-white border-b-8 border-b-transparent ml-1"></div>
                      </div>
                    </div>
                  </div>

                  <div className="flex-1">
                    <h2 className="text-xl font-space font-bold mb-3">
                      Trevor Noah: My Depression Was Linked To ADHD! Why I Left
                      The Daily Show!
                    </h2>
                    <div className="flex items-center gap-3 mb-4 text-sm">
                      <div className="flex items-center gap-1 text-gray-400">
                        <Clock className="w-4 h-4" />
                        <span className="text-gray-400">8:45 minutes</span>
                      </div>
                      <div className="h-1 w-1 rounded-full bg-gray-600"></div>
                      <span className="text-gray-400">May 15, 2023</span>
                    </div>
                    <p
                      className={`text-sm mb-4 ${
                        theme === "dark" ? "text-gray-300" : "text-gray-600"
                      }`}
                    >
                      This summary was generated by Nobot AI from a YouTube
                      video. It captures the key points and insights from the
                      content.
                    </p>

                    <div className="flex flex-wrap gap-2">
                      <span
                        className={`text-xs px-2 py-1 rounded-full ${
                          theme === "dark"
                            ? "bg-nobot-purple/20 text-nobot-purple"
                            : "bg-nobot-purple/10 text-nobot-purple"
                        }`}
                      >
                        Interview
                      </span>
                      <span
                        className={`text-xs px-2 py-1 rounded-full ${
                          theme === "dark"
                            ? "bg-nobot-green/20 text-nobot-green"
                            : "bg-nobot-green/10 text-nobot-green"
                        }`}
                      >
                        Biography
                      </span>
                      <span
                        className={`text-xs px-2 py-1 rounded-full ${
                          theme === "dark"
                            ? "bg-nobot-pink/20 text-nobot-pink"
                            : "bg-nobot-pink/10 text-nobot-pink"
                        }`}
                      >
                        Comedy
                      </span>
                    </div>
                  </div>
                </div>

                <div className="p-3 rounded-lg mb-6 flex items-center justify-between bg-gray-800">
                  <div className="flex gap-2">
                    <button
                      className={`px-3 py-1.5 text-sm rounded-md ${
                        activeTab === "bullet"
                          ? "bg-gray-700 shadow-sm text-white"
                          : "text-gray-300 hover:text-white"
                      }`}
                      onClick={() => setActiveTab("bullet")}
                    >
                      Bullet Points
                    </button>
                    <button
                      className={`px-3 py-1.5 text-sm rounded-md ${
                        activeTab === "paragraph"
                          ? `${
                              theme === "dark" ? "bg-nobot-gray" : "bg-white"
                            } shadow-sm`
                          : ""
                      }`}
                      onClick={() => setActiveTab("paragraph")}
                    >
                      Paragraph
                    </button>
                  </div>

                  <div className="flex items-center gap-2">
                    <div className="flex items-center gap-1">
                      <span className="text-xs">Reading Level:</span>
                    </div>
                    <div className="flex text-xs bg-gray-700 rounded-md overflow-hidden">
                      {(["simple", "detailed", "advanced"] as const).map(
                        (level) => (
                          <button
                            key={level}
                            className={`px-2 py-1 ${
                              readingLevel === level
                                ? "bg-gradient-to-r from-purple-500 to-pink-500 text-white"
                                : "text-gray-300 hover:bg-gray-600"
                            }`}
                            onClick={() => {}}
                          >
                            {level.charAt(0).toUpperCase() + level.slice(1)}
                          </button>
                        )
                      )}
                    </div>
                  </div>
                </div>

                <div className="space-y-8 mb-8">
                  {activeTab === "bullet" ? (
                    summaryPoints.map((section, index) => (
                      <div key={index} className="space-y-3">
                        <h4 className="font-bold text-lg flex items-center gap-2">
                          <span
                            className={`h-3 w-3 rounded-full ${
                              index === 0 ? "bg-purple-400" : "bg-pink-400"
                            }`}
                          ></span>
                          {section.title}
                        </h4>
                        <ul className="space-y-3">
                          {section.points.map((point, pointIndex) => (
                            <li key={pointIndex} className="flex gap-3">
                              <div className="mt-1 h-5 w-5 rounded-full flex-shrink-0 border-2 border-purple-500/30 flex items-center justify-center">
                                <div
                                  className={`h-2.5 w-2.5 rounded-full ${
                                    pointIndex === 0
                                      ? "bg-purple-400"
                                      : pointIndex === 1
                                      ? "bg-pink-400"
                                      : "bg-purple-300"
                                  }`}
                                ></div>
                              </div>
                              <p className="text-sm text-gray-300">{point}</p>
                            </li>
                          ))}
                        </ul>
                      </div>
                    ))
                  ) : (
                    <div className="p-6 rounded-lg bg-gray-800">
                      <p className="text-sm leading-relaxed">
                        {paragraphSummary}
                      </p>
                    </div>
                  )}
                </div>

                {/* Action buttons */}
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center pt-6 border-t border-gray-700 gap-4">
                  <div className="flex flex-wrap items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-purple-500 text-purple-400 hover:bg-purple-500/10 hover:text-purple-300"
                    >
                      <Copy className="h-3.5 w-3.5 mr-1" />
                      Copy
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-purple-500 text-purple-400 hover:bg-purple-500/10 hover:text-purple-300"
                    >
                      <Share2 className="h-3.5 w-3.5 mr-1" />
                      Share
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-pink-500 text-pink-400 hover:bg-pink-500/10 hover:text-pink-300"
                    >
                      <DownloadCloud className="h-3.5 w-3.5 mr-1" />
                      Export
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-pink-500 text-pink-400 hover:bg-pink-500/10 hover:text-pink-300"
                    >
                      <Bookmark className="h-3.5 w-3.5 mr-1" />
                      Save
                    </Button>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-gray-400">
                      Was this summary helpful?
                    </span>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 text-gray-400 hover:text-green-400"
                    >
                      <ThumbsUp className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 text-gray-400 hover:text-pink-400"
                    >
                      <ThumbsDown className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      )}

      {/* Popular Summaries Section */}
      <div className="max-w-4xl mx-auto mt-12">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-space font-bold">Popular Summaries</h2>
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-400">Filter by:</span>
            </div>
            <div className="flex gap-2 p-1 rounded-lg bg-gray-800">
              {["all", "tech", "news", "science", "entertainment"].map(
                (filter) => (
                  <button
                    key={filter}
                    className={`px-3 py-1 text-xs rounded-md transition-colors ${
                      activeFilter === filter
                        ? "bg-gradient-to-r from-purple-500 to-pink-500 text-white"
                        : "text-gray-400 hover:bg-gray-700 hover:text-white"
                    }`}
                    onClick={() => setActiveFilter(filter)}
                  >
                    {filter.charAt(0).toUpperCase() + filter.slice(1)}
                  </button>
                )
              )}
            </div>
          </div>
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          {[
            {
              title:
                "Trevor Noah: My Depression Was Linked To ADHD! Why I Left The Daily Show!",
              thumbnail:
                "https://i3.ytimg.com/vi/yGLzpt3caXA/maxresdefault.jpg",
              category: "entertainment",
            },
            {
              title:
                "How ChatGPT Works: The Architecture Behind the Revolution",
              thumbnail:
                "https://i3.ytimg.com/vi/bZQun8Y4L2A/maxresdefault.jpg",
              category: "tech",
            },
            {
              title: "The Science of Sleep: How to Get Better Rest",
              thumbnail:
                "https://i3.ytimg.com/vi/gbQFSMayJxk/maxresdefault.jpg",
              category: "science",
            },
            {
              title: "Global Economy Update: Latest Trends and Forecasts",
              thumbnail:
                "https://i3.ytimg.com/vi/dZL25NSLhEA/maxresdefault.jpg",
              category: "news",
            },
          ].map((item, index) => (
            <div
              key={index}
              className="rounded-lg overflow-hidden bg-gray-800 hover:bg-gray-700 transition-all cursor-pointer shadow-lg"
              style={{
                background: "linear-gradient(145deg, #1f2937 0%, #111827 100%)",
                border: "1px solid #374151",
              }}
              onClick={() => {
                setShowSummary(true);
                setSummaryLoading(true);
                setTimeout(() => setSummaryLoading(false), 1500);
              }}
            >
              <div className="relative">
                <Image
                  src={item.thumbnail}
                  alt={item.title}
                  className="w-full h-48 object-cover"
                  width={320}
                  height={192}
                  unoptimized={!item.thumbnail.startsWith('/')}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent flex items-end p-4">
                  <span
                    className={`text-xs px-2 py-1 rounded-full ${
                      item.category === "tech"
                        ? "bg-gradient-to-r from-purple-500 to-pink-500 text-white"
                        : item.category === "science"
                        ? "bg-gradient-to-r from-purple-400 to-blue-500 text-white"
                        : item.category === "entertainment"
                        ? "bg-gradient-to-r from-pink-500 to-red-500 text-white"
                        : "bg-gradient-to-r from-blue-500 to-cyan-500 text-white"
                    }`}
                  >
                    {item.category.charAt(0).toUpperCase() +
                      item.category.slice(1)}
                  </span>
                </div>
              </div>
              <div className="p-4">
                <h3 className="font-medium text-sm line-clamp-2">
                  {item.title}
                </h3>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default NobotSummary;
