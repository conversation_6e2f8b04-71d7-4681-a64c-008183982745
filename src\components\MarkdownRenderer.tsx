'use client';

import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

interface MarkdownRendererProps {
  content: string;
  className?: string;
}

export default function MarkdownRenderer({ content, className = '' }: MarkdownRendererProps) {
  // Process content to ensure proper spacing for emojis and bullet points
  const processedContent = React.useMemo(() => {
    if (!content) return '';

    // Ensure proper spacing after emojis in bullet points
    // This regex looks for emoji followed by text without proper spacing
    return content
      // Ensure there's exactly one space after each emoji at the start of a line
      .replace(/^([\p{Emoji}\p{Emoji_Presentation}]+)([^\s])/gmu, '$1 $2')
      // Ensure there's a blank line between bullet points that start with emoji
      .replace(/\n([\p{Emoji}\p{Emoji_Presentation}]+\s[^\n]+)\n([\p{Emoji}\p{Emoji_Presentation}]+\s)/gmu, '\n$1\n\n$2');
  }, [content]);
  return (
    <div className={`markdown-content ${className}`} style={{
      fontSize: '16px',
      fontStyle: 'normal',
      fontWeight: 400,
      letterSpacing: '-0.08px',
      lineHeight: '22px',
      color: 'var(--foreground)'
    }}>
      <ReactMarkdown remarkPlugins={[remarkGfm]}>
        {processedContent}
      </ReactMarkdown>
    </div>
  );
}
