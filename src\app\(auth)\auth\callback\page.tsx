'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { createPagesBrowserClient } from '@supabase/auth-helpers-nextjs'

export default function AuthCallback() {
  const router = useRouter()

  useEffect(() => {
    const supabase = createPagesBrowserClient()

    supabase.auth.getSession().then(({ data: { session } }) => {
      console.log('Client Session:', session)
      if (session) {
        router.replace('/profile')
      } else {
        router.replace('/login')
      }
    })
  }, [router])

  return <p>Redirecting...</p>
}
