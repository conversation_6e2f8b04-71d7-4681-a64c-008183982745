import { redirect } from 'next/navigation'
import { createSupabaseServerClient } from '@/lib/supabase/server'

export default async function ProfilePage() {
  const supabase = createSupabaseServerClient()
  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) redirect('/login')

  return (
    <div>
      <h1>Welcome, {user.email}</h1>
      <form action="/logout" method="post">
        <button type="submit">Logout</button>
      </form>
    </div>
  )
}
