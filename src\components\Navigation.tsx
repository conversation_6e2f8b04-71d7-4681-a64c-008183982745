'use client';

import { usePathname } from 'next/navigation';
import Link from 'next/link';
import Mascot from './Mascot';

const AUTH_PAGES = ['/login', '/signup', '/auth'];

export function Navigation() {
  const pathname = usePathname();
  const isAuthPage = AUTH_PAGES.some(page => pathname?.startsWith(page));

  if (isAuthPage) return null;

  return (
    <nav className="flex items-center justify-between p-6 max-w-7xl mx-auto">
      <Link href="/" className="flex items-center space-x-3">
        <Mascot size="sm" expression="happy" />
        <span className="font-bold text-xl bg-clip-text text-white">
          Nobot
        </span>
      </Link>

      <div className="hidden md:flex items-center space-x-8">
        {pathname === '/' ? (
          <>
            <a
              href="#how-it-works"
              className="text-gray-300 hover:text-white transition-colors"
            >
              How it Works
            </a>
            <a
              href="#why-nobot"
              className="text-gray-300 hover:text-white transition-colors"
            >
              Why Nobot
            </a>
            <Link
              href="/summary"
              className="text-gray-300 hover:text-white transition-colors"
            >
              Summary
            </Link>
            <Link
              href="/pricing"
              className="text-gray-300 hover:text-white transition-colors"
            >
              Pricing
            </Link>
          </>
        ) : (
          <>
            <Link
              href="/"
              className="text-gray-300 hover:text-white transition-colors"
            >
              Home
            </Link>
            <Link
              href="/summary"
              className="text-gray-300 hover:text-white transition-colors"
            >
              Summary
            </Link>
            <Link
              href="/pricing"
              className="text-gray-300 hover:text-white transition-colors"
            >
              Pricing
            </Link>
          </>
        )}
      </div>

      <div className="flex items-center space-x-4">
        <Link href="/login" className="bg-gradient-to-r from-purple-500 to-pink-500 px-6 py-2.5 rounded-lg font-medium hover:from-purple-600 hover:to-pink-600 transition-all shadow-lg shadow-purple-500/20 hover:shadow-purple-500/40">
          Login
        </Link>
      </div>
    </nav>
  );
}
