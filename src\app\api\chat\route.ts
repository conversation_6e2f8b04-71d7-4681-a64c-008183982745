import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';
import { env, validateEnv } from '@/lib/env';

// Validate environment variables
validateEnv();

// Simple constant to define which model to use (same as in actions.ts)
const SELECTED_MODEL = env.GPT_MODEL_MINI;

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: env.OPENAI_API_KEY,
});

interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

export async function POST(request: NextRequest) {
  try {
    const { message, transcript, videoTitle, chatHistory } = await request.json();

    if (!message || !transcript) {
      return NextResponse.json(
        { error: 'Message and transcript are required' },
        { status: 400 }
      );
    }

    // Prepare the conversation context
    const systemPrompt = `You are a helpful AI assistant that answers questions about YouTube videos based on their transcripts. 

Video Title: ${videoTitle || 'Unknown'}

Video Transcript:
"""
${transcript}
"""

Instructions:
- Answer questions based ONLY on the information provided in the transcript
- If the transcript doesn't contain information to answer a question, politely say so
- Be conversational and helpful
- Provide specific quotes or references from the transcript when relevant
- If asked about timestamps, refer to the timestamp markers in the transcript (e.g., [00:15])
- Keep responses concise but informative
- Use markdown formatting for better readability when appropriate`;

    // Build conversation history
    const messages: any[] = [
      { role: 'system', content: systemPrompt }
    ];

    // Add recent chat history for context (last 10 messages)
    if (chatHistory && Array.isArray(chatHistory)) {
      chatHistory.slice(-10).forEach((msg: ChatMessage) => {
        messages.push({
          role: msg.role,
          content: msg.content
        });
      });
    }

    // Add the current user message
    messages.push({
      role: 'user',
      content: message
    });

    // Call OpenAI API
    const completion = await openai.chat.completions.create({
      model: SELECTED_MODEL,
      messages,
      max_tokens: 1000,
      temperature: 0.7,
    });

    const response = completion.choices[0]?.message?.content;

    if (!response) {
      throw new Error('No response from OpenAI');
    }

    return NextResponse.json({ response });

  } catch (error: any) {
    console.error('Chat API error:', error);
    
    // Handle specific OpenAI errors
    if (error.code === 'insufficient_quota') {
      return NextResponse.json(
        { error: 'OpenAI API quota exceeded. Please check your billing settings.' },
        { status: 429 }
      );
    }
    
    if (error.status === 401) {
      return NextResponse.json(
        { error: 'OpenAI API key is invalid or missing.' },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to process chat message. Please try again.' },
      { status: 500 }
    );
  }
}
