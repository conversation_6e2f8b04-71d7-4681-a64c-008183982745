'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';
import { OutputLanguage, OutputStyle, OutputSize, LogEntry } from '@/app/actions';

// Define the context type
interface FormContextType {
  // Form inputs
  videoUrl: string;
  setVideoUrl: (url: string) => void;
  language: OutputLanguage;
  setLanguage: (language: OutputLanguage) => void;
  style: OutputStyle;
  setStyle: (style: OutputStyle) => void;
  size: OutputSize;
  setSize: (size: OutputSize) => void;

  // Results
  post: string | null;
  setPost: (post: string | null) => void;
  transcript: string | null;
  setTranscript: (transcript: string | null) => void;
  transcriptSource: 'youtube-captions' | 'audio-transcription' | null;
  setTranscriptSource: (source: 'youtube-captions' | 'audio-transcription' | null) => void;
  modelUsed: string | null;
  setModelUsed: (model: string | null) => void;

  // Video metadata
  videoTitle: string | null;
  setVideoTitle: (title: string | null) => void;
  videoThumbnail: string | null;
  setVideoThumbnail: (url: string | null) => void;
  videoId: string | null;
  setVideoId: (id: string | null) => void;

  // UI state
  loading: boolean;
  setLoading: (loading: boolean) => void;
  error: string | null;
  setError: (error: string | null) => void;
  logs: LogEntry[];
  setLogs: (logs: LogEntry[]) => void;
  showLogs: boolean;
  setShowLogs: (show: boolean) => void;

  // Functions
  addLog: (level: string, message: string) => void;
  handleSubmit: (e: React.FormEvent) => Promise<void>;
  getVideoInfo: (url: string) => Promise<void>;
}

// Create the context with a default undefined value
const FormContext = createContext<FormContextType | undefined>(undefined);

// Provider component
export function FormProvider({ children }: { children: ReactNode }) {
  // Form inputs
  const [videoUrl, setVideoUrl] = useState('');
  const [language, setLanguage] = useState<OutputLanguage>('english');
  const [style, setStyle] = useState<OutputStyle>('insightful');
  const [size, setSize] = useState<OutputSize>('auto');

  // Results
  const [post, setPost] = useState<string | null>(null);
  const [transcript, setTranscript] = useState<string | null>(null);
  const [transcriptSource, setTranscriptSource] = useState<'youtube-captions' | 'audio-transcription' | null>(null);
  const [modelUsed, setModelUsed] = useState<string | null>(null);

  // Video metadata
  const [videoTitle, setVideoTitle] = useState<string | null>(null);
  const [videoThumbnail, setVideoThumbnail] = useState<string | null>(null);
  const [videoId, setVideoId] = useState<string | null>(null);

  // UI state
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [showLogs, setShowLogs] = useState(false);

  // Function to add a log entry
  const addLog = (level: string, message: string) => {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0]; // HH:MM:SS format
    setLogs(prevLogs => [...prevLogs, { timestamp, level, message }]);
  };

  // Form submission handler
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Reset previous state
    setLogs([]);
    setError(null);
    setPost(null);
    setTranscript(null);
    setTranscriptSource(null);

    // Add initial client-side logs
    addLog('INFO', 'Starting YouTube video processing');
    addLog('INFO', `Input: URL=${videoUrl}, Language=${language}, Summary Style=${style}, Size=${size}, Model=GPT-4o Mini`);

    if (!videoUrl) {
      setError('Please enter a YouTube URL');
      addLog('ERROR', 'No YouTube URL provided');
      return;
    }

    // Basic URL validation
    if (!videoUrl.includes('youtube.com/') && !videoUrl.includes('youtu.be/')) {
      setError('Please enter a valid YouTube URL');
      addLog('ERROR', `Invalid URL format: ${videoUrl}`);
      return;
    }

    // Get video info (thumbnail and placeholder title)
    await getVideoInfo(videoUrl);

    try {
      setLoading(true);
      addLog('INFO', 'Sending request to server...');

      // Set up a timer to add "still processing" logs every 5 seconds
      // but only if we haven't received server logs yet
      let receivedServerLogs = false;
      const processingInterval = setInterval(() => {
        if (!receivedServerLogs) {
          addLog('INFO', 'Still processing, please wait...');
        }
      }, 5000);

      // Dynamically import the server action to avoid client-side execution
      const { processYouTubeVideo } = await import('@/app/actions');
      const result = await processYouTubeVideo(
        videoUrl,
        language,
        style,
        size,
        true // enableAudioTranscription
      );

      // Clear the interval
      clearInterval(processingInterval);
      receivedServerLogs = true;

      // Replace client logs with server logs
      setLogs(result.logs);

      // Set the results
      setPost(result.post);
      setTranscript(result.transcript);
      setTranscriptSource(result.transcriptSource);
      setModelUsed(result.modelUsed);

      // Set video title directly from the response if available
      if (result.videoTitle) {
        console.log('Setting video title from response:', result.videoTitle);
        setVideoTitle(result.videoTitle);
      } else {
        // Fallback: Try to extract video title from logs
        console.log('No video title in response, trying to extract from logs');
        const titleLog = result.logs.find(log =>
          log.message.includes('Video information retrieved:')
        );

        if (titleLog) {
          const titleMatch = titleLog.message.match(/Video information retrieved: (.+)/);
          if (titleMatch && titleMatch[1]) {
            setVideoTitle(titleMatch[1]);
            console.log('Video title extracted from logs:', titleMatch[1]);
          } else {
            console.log('Title match pattern failed:', titleLog.message);
          }
        } else {
          console.log('No log entry with video title found');

          // Fallback: Try to find any log that might contain video information
          const possibleTitleLogs = result.logs.filter(log =>
            log.message.toLowerCase().includes('video') &&
            log.message.toLowerCase().includes('title')
          );

          if (possibleTitleLogs.length > 0) {
            console.log('Possible title logs found:', possibleTitleLogs);

            // Try to extract from "Video title extracted" log
            const extractedTitleLog = possibleTitleLogs.find(log =>
              log.message.includes('Video title extracted:')
            );

            if (extractedTitleLog) {
              const extractMatch = extractedTitleLog.message.match(/Video title extracted: (.+)/);
              if (extractMatch && extractMatch[1]) {
                setVideoTitle(extractMatch[1]);
                console.log('Video title extracted from "Video title extracted" log:', extractMatch[1]);
              }
            }
          }
        }
      }

      // Add model information to logs
      addLog('INFO', `Summary generated using model: ${result.modelUsed}`);

      // Add logs from the server
      if (result.logs && Array.isArray(result.logs)) {
        result.logs.forEach((log: LogEntry) => {
          addLog(log.level, log.message);
        });
      }
    } catch (err) {
      console.error('Error in form submission:', err);
      addLog('ERROR', `Processing failed: ${err instanceof Error ? err.message : 'Unknown error'}`);

      // Handle specific error cases
      if (err instanceof Error) {
        if (err.message.includes('403')) {
          setError('OpenAI API access forbidden (403 error): This usually means your OpenAI account does not have billing set up, which is required for using the Whisper API, or your API key is invalid. Please check your OpenAI account settings.');
          addLog('ERROR', 'OpenAI API access forbidden (403 error)');
        } else {
          setError(err.message);
        }
      } else {
        setError('An unexpected error occurred. Please try again later.');
      }
    } finally {
      setLoading(false);
    }
  };

  // Function to extract video ID from YouTube URL
  const extractVideoId = (url: string): string | null => {
    let videoId: string | null = null;

    // Handle youtube.com URLs
    if (url.includes('youtube.com/watch')) {
      const urlParams = new URL(url).searchParams;
      videoId = urlParams.get('v');
    }
    // Handle youtu.be URLs
    else if (url.includes('youtu.be/')) {
      videoId = url.split('youtu.be/')[1];
      const questionMarkPosition = videoId.indexOf('?');
      if (questionMarkPosition !== -1) {
        videoId = videoId.substring(0, questionMarkPosition);
      }
    }

    return videoId;
  };

  // Function to get video info (title and thumbnail)
  const getVideoInfo = async (url: string): Promise<void> => {
    try {
      // Reset previous video info but keep the UI stable
      setVideoTitle('Loading video information...');

      // We'll keep the thumbnail until we have a new one
      // This prevents flickering in the UI

      // Extract video ID
      const id = extractVideoId(url);
      if (!id) {
        addLog('WARN', 'Could not extract video ID from URL');
        return;
      }

      setVideoId(id);

      // Set thumbnail URL (using high quality thumbnail)
      const thumbnailUrl = `https://img.youtube.com/vi/${id}/hqdefault.jpg`;
      setVideoThumbnail(thumbnailUrl);

      // Set a placeholder title initially
      setVideoTitle('Loading video information...');

      // Try to fetch the video title immediately using the YouTube oEmbed API
      try {
        addLog('INFO', 'Fetching video title from YouTube oEmbed API');
        const response = await fetch(`https://www.youtube.com/oembed?url=https://www.youtube.com/watch?v=${id}&format=json`);

        if (response.ok) {
          const data = await response.json();
          if (data.title) {
            setVideoTitle(data.title);
            addLog('INFO', `Video title fetched: ${data.title}`);
            console.log('Video title fetched from oEmbed API:', data.title);
          }
        } else {
          console.log('Failed to fetch video title from oEmbed API:', response.status);
          addLog('WARN', `Failed to fetch video title from oEmbed API: ${response.status}`);
        }
      } catch (titleError) {
        console.error('Error fetching video title:', titleError);
        addLog('WARN', 'Error fetching video title from oEmbed API');
      }
    } catch (error) {
      console.error('Error getting video info:', error);
      addLog('WARN', 'Failed to get video information');
    }
  };

  // Create the context value object
  const contextValue: FormContextType = {
    videoUrl, setVideoUrl,
    language, setLanguage,
    style, setStyle,
    size, setSize,
    post, setPost,
    transcript, setTranscript,
    transcriptSource, setTranscriptSource,
    modelUsed, setModelUsed,
    videoTitle, setVideoTitle,
    videoThumbnail, setVideoThumbnail,
    videoId, setVideoId,
    loading, setLoading,
    error, setError,
    logs, setLogs,
    showLogs, setShowLogs,
    addLog,
    handleSubmit,
    getVideoInfo
  };

  return (
    <FormContext.Provider value={contextValue}>
      {children}
    </FormContext.Provider>
  );
}

// Custom hook to use the form context
export function useFormContext() {
  const context = useContext(FormContext);
  if (context === undefined) {
    throw new Error('useFormContext must be used within a FormProvider');
  }
  return context;
}
