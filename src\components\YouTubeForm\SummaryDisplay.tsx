'use client';

import Mark<PERSON><PERSON>enderer from '../MarkdownRenderer';
import { useFormContext } from './FormContext';

export default function SummaryDisplay() {
  const { post } = useFormContext();

  if (!post) return null;

  return (
    <div className="space-y-4">
      {/* <div className="flex justify-between items-center border-b pb-2">
        <h2 className="text-xl font-semibold">Smart Summary</h2>
        <div className="flex items-center gap-2">
          {modelUsed && (
            <div className={`text-xs px-2 py-1 rounded-full ${
              modelUsed.includes('gpt-4')
                ? 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200'
                : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
            }`}>
              {modelUsed.includes('gpt-4') ? '✨ Premium' : '🔄 Standard'}
            </div>
          )}
          <div className="flex gap-1">
            <button className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M12 3v12" />
                <path d="m8 11 4 4 4-4" />
                <path d="M8 5H4a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2h-4" />
              </svg>
            </button>
            <button className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <rect width="14" height="14" x="8" y="8" rx="2" ry="2" />
                <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2" />
              </svg>
            </button>
          </div>
        </div>
      </div> */}

      <div
        className="overflow-y-auto max-h-[440px] pr-2 [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-thumb]:bg-gray-600 hover:[&::-webkit-scrollbar-thumb]:bg-gray-400 dark:hover:[&::-webkit-scrollbar-thumb]:bg-gray-500"
        style={{
          fontFamily: 'var(--font-inter)',
          fontSize: '16px',
          fontStyle: 'normal',
          fontWeight: 400,
          letterSpacing: '-0.08px',
          lineHeight: '22px',
          scrollbarWidth: 'thin',
          scrollbarColor: 'var(--gray-300) transparent',
          msOverflowStyle: 'none'  /* IE and Edge */
        }}
      >
        <div className="pr-2 text-white">
          <MarkdownRenderer content={post} />
        </div>
      </div>

      {/* <div className="flex flex-wrap gap-2">
        <Button
          onClick={() => {
            navigator.clipboard.writeText(post);
          }}
          variant="outline"
          size="sm"
          className="flex items-center gap-1"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <rect width="14" height="14" x="8" y="8" rx="2" ry="2" />
            <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2" />
          </svg>
          Copy
        </Button>

        {modelUsed && (
          <div className="flex items-center text-xs text-muted-foreground">
            <span>
              {modelUsed.includes('gpt-4')
                ? '✨ Model: '
                : '🔄 Model: '
              }
              <code className="bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded">{modelUsed}</code>
            </span>
          </div>
        )}
      </div> */}
    </div>
  );
}
