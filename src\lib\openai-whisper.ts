import OpenAI from 'openai';
import * as fs from 'fs';
import * as logger from './logger';
import { env } from '@/lib/env';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: env.OPENAI_API_KEY,
});

/**
 * Map language code from our application to OpenAI Whisper language code
 * @param language Language from our application
 * @returns OpenAI Whisper language code
 */
export function mapLanguageToWhisperCode(language: string): string {
  const languageMap: Record<string, string> = {
    'english': 'en',
    'hindi': 'hi',
    'spanish': 'es',
    'french': 'fr',
    'german': 'de',
    'chinese': 'zh',
    'japanese': 'ja',
  };

  return languageMap[language.toLowerCase()] || 'en';
}

/**
 * Transcribe audio using OpenAI Whisper API
 * @param audioPath Path to the audio file
 * @param languageCode Language code (default: 'en')
 * @returns Transcribed text
 */
export async function transcribeWithWhisper(
  audioPath: string,
  languageCode: string = 'en'
): Promise<string> {
  logger.info(`Starting OpenAI Whisper transcription for file: ${audioPath}`);
  logger.info(`Using language code: ${languageCode}`);

  try {
    // Check if the file exists
    if (!fs.existsSync(audioPath)) {
      logger.error(`Audio file not found: ${audioPath}`);
      throw new Error(`Audio file not found: ${audioPath}`);
    }

    // Log file stats
    const stats = fs.statSync(audioPath);
    logger.info(`Audio file stats:`, {
      size: `${(stats.size / 1024 / 1024).toFixed(2)} MB`,
      created: stats.birthtime,
      path: audioPath
    });

    // Check if OpenAI API key is set
    if (!env.OPENAI_API_KEY) {
      logger.error('OpenAI API key is not set');
      throw new Error('OpenAI API key is not set. Please set the OPENAI_API_KEY environment variable.');
    }

    logger.info('Sending transcription request to OpenAI Whisper API');
    const startTime = Date.now();

    // Create a readable stream from the audio file
    const audioFile = fs.createReadStream(audioPath);

    // Call OpenAI Whisper API
    const transcription = await openai.audio.transcriptions.create({
      file: audioFile,
      model: 'whisper-1',
      language: languageCode,
      response_format: 'text',
    });

    const duration = ((Date.now() - startTime) / 1000).toFixed(2);
    logger.info(`OpenAI Whisper API response received in ${duration}s`);

    // Process the response
    if (!transcription || typeof transcription !== 'string') {
      logger.warn('OpenAI Whisper returned empty or invalid transcription');
      throw new Error('Transcription returned empty or invalid text');
    }

    logger.info(`Complete transcription (${transcription.length} characters):`);
    logger.info(`"${transcription}"`);

    logger.info(`Transcription successful: ${transcription.length} characters`);
    logger.debug('Transcription preview:', {
      preview: transcription.substring(0, 100) + '...'
    });

    return transcription;
  } catch (error: any) {
    logger.error('Error in OpenAI Whisper transcription:', {
      error: error.message,
      stack: error.stack
    });

    // Provide more specific error messages
    if (error.message.includes('API key')) {
      throw new Error('OpenAI API key is invalid or not set. Please check your API key.');
    } else if (error.message.includes('billing')) {
      throw new Error('OpenAI API billing is not set up. Please set up billing in your OpenAI account.');
    } else if (error.message.includes('ENOENT')) {
      throw new Error(`File not found: ${audioPath}`);
    } else {
      throw new Error(`OpenAI Whisper error: ${error.message}`);
    }
  }
}
