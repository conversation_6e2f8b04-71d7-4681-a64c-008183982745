// Optimized logger utility for tracking application flow with minimal performance impact

// Log levels
export enum LogLevel {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR',
}

// Import environment variables
import { env } from '@/lib/env';

// Current log level (from environment variables)
const currentLogLevel = (env.LOG_LEVEL as LogLevel) || LogLevel.INFO;

// Production mode detection
export const isProduction = env.NODE_ENV === 'production';

// Log level priority
const logLevelPriority = {
  [LogLevel.DEBUG]: 0,
  [LogLevel.INFO]: 1,
  [LogLevel.WARN]: 2,
  [LogLevel.ERROR]: 3,
};

// Check if a log level should be displayed (exported for conditional logging)
export const shouldLog = (level: LogLevel): boolean => {
  return logLevelPriority[level] >= logLevelPriority[currentLogLevel as LogLevel];
};

// Format the current timestamp (simplified for performance)
const getTimestamp = (): string => {
  // In production, use a simpler timestamp format
  if (isProduction) {
    return new Date().toISOString().split('T')[1].split('.')[0]; // HH:MM:SS format
  }
  return new Date().toISOString();
};

// Safely truncate data objects to prevent large object logging
const safelyStringifyData = (data: any, maxLength = 1000): string | any => {
  if (data === undefined || data === null) return '';

  if (typeof data === 'object') {
    try {
      // For objects that might be large, stringify and truncate
      const stringified = JSON.stringify(data);
      if (stringified.length > maxLength) {
        return stringified.substring(0, maxLength) + '... [truncated]';
      }
      return data; // Return original if not too large
    } catch {
      return '[Object cannot be stringified]';
    }
  }

  return data;
};

// Main logger function with support for lazy evaluation
export const log = (level: LogLevel, messageOrFn: string | (() => string), data?: any): void => {
  // Early return if this log level shouldn't be shown
  if (!shouldLog(level)) return;

  // Evaluate message if it's a function (lazy evaluation)
  const message = typeof messageOrFn === 'function' ? messageOrFn() : messageOrFn;
  const timestamp = getTimestamp();
  const logMessage = `[${timestamp}] [${level}] ${message}`;

  // Process data to prevent performance issues with large objects
  const processedData = data ? safelyStringifyData(data) : '';

  switch (level) {
    case LogLevel.DEBUG:
      console.debug(logMessage, processedData);
      break;
    case LogLevel.INFO:
      console.info(logMessage, processedData);
      break;
    case LogLevel.WARN:
      console.warn(logMessage, processedData);
      break;
    case LogLevel.ERROR:
      console.error(logMessage, processedData);
      break;
  }
};

// Convenience methods with support for lazy evaluation
export const debug = (messageOrFn: string | (() => string), data?: any): void => {
  if (shouldLog(LogLevel.DEBUG)) {
    log(LogLevel.DEBUG, messageOrFn, data);
  }
};

export const info = (messageOrFn: string | (() => string), data?: any): void => {
  if (shouldLog(LogLevel.INFO)) {
    log(LogLevel.INFO, messageOrFn, data);
  }
};

export const warn = (messageOrFn: string | (() => string), data?: any): void => {
  if (shouldLog(LogLevel.WARN)) {
    log(LogLevel.WARN, messageOrFn, data);
  }
};

export const error = (messageOrFn: string | (() => string), data?: any): void => {
  if (shouldLog(LogLevel.ERROR)) {
    log(LogLevel.ERROR, messageOrFn, data);
  }
};

// Performance timing utilities
interface TimerData {
  startTime: number;
  label: string;
}

const timers: Map<string, TimerData> = new Map();

// Start a performance timer
export const startTimer = (label: string): void => {
  if (!shouldLog(LogLevel.DEBUG)) return;
  timers.set(label, { startTime: Date.now(), label });
};

// End a performance timer and log the result
export const endTimer = (label: string, level: LogLevel = LogLevel.DEBUG): void => {
  if (!shouldLog(level)) return;

  const timer = timers.get(label);
  if (timer) {
    const duration = ((Date.now() - timer.startTime) / 1000).toFixed(2);
    log(level, `${label} completed in ${duration}s`);
    timers.delete(label);
  }
};
