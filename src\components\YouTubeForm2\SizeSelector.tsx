"use client";

import { Filter } from "lucide-react";
import { useFormContext } from "./FormContext";

export default function SizeSelector() {
  const { size, setSize } = useFormContext();

  return (
    <>
      <div className="flex flex-wrap items-center gap-2 w-full sm:w-auto">
        <div className="flex items-center gap-1">
          <Filter className="w-3.5 h-3.5 flex-shrink-0" />
          <span className="text-xs whitespace-nowrap">Reading Level:</span>
        </div>
        <div className="flex text-xs bg-gray-700 rounded-md overflow-hidden whitespace-nowrap">
          {(
            [
              { label: "simple", value: "concise" },
              { label: "detailed", value: "auto" },
              { label: "advanced", value: "detailed" },
            ] as const
          ).map((level) => (
            <button
              key={level.value}
              className={`px-2 py-1 ${
                size === level.value
                  ? "bg-gradient-to-r from-purple-500 to-pink-500 text-white"
                  : "text-gray-300 hover:bg-gray-600"
              }`}
              onClick={() => setSize(level.value)}
            >
              {level.label.charAt(0).toUpperCase() + level.label.slice(1)}
            </button>
          ))}
        </div>
      </div>
    </>
  );
}
