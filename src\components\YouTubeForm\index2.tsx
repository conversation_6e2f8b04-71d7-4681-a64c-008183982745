"use client";

import React from "react";
import { FormProvider, useFormContext } from "./FormContext";
import YouTubeUrlInput from "./YouTubeUrlInput";
// import ModelSelector from './ModelSelector';
import ErrorDisplay from "./ErrorDisplay";
// import LogsButton from './LogsButton';
import LogDisplay from "../LogDisplay";
import VideoPreview from "./VideoPreview";
import VideoThumbnails, { dummyThumbnails } from "./VideoThumbnails";
import TabsContainer from "./TabsContainer";
import { BookOpen } from "lucide-react";
import NobotSummary from "./NobotSummary";
// import InfoSection from './InfoSection';

// Form component that uses the context
function YouTubeFormContent() {
  const {
    handleSubmit,
    logs,
    showLogs,
    setVideoTitle,
    setVideoThumbnail,
    setVideoId,
    setPost,
    videoId,
  } = useFormContext();

  const handleThumbnailClick = (data: {
    title: string;
    summary: string;
    thumbnail: string;
    videoId: string;
  }) => {
    setVideoTitle(data.title);
    setVideoThumbnail(data.thumbnail);
    setVideoId(data.videoId);
    setPost(data.summary);
    // Don't update the URL input field when clicking thumbnails
    // setVideoUrl(`https://youtu.be/${data.videoId}`);
  };

  // Set default video preview on component mount if no video is selected
  React.useEffect(() => {
    if (!videoId && dummyThumbnails.length > 0) {
      const defaultThumbnail = dummyThumbnails[0];
      setVideoTitle(defaultThumbnail.title);
      setVideoThumbnail(defaultThumbnail.url);
      setVideoId(String(defaultThumbnail.id)); // Ensure id is converted to string
      setPost(defaultThumbnail.summary);
    }
  }, [setVideoTitle, setVideoThumbnail, setVideoId, setPost, videoId]);

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <main>
        <section className="pt-16 flex justify-center pb-16 relative hero-gradient overflow-hidden">
          <div className="container relative">
            <div className="mx-auto text-center mb-10">
              <div className="inline-block p-2 rounded-lg bg-purple-500/10 mb-4">
                <BookOpen className="w-6 h-6 text-purple-400" />
              </div>
              <h1 className="text-6xl md:text-7xl font-bold mb-6 leading-tight">
                Get Instant{" "}
                <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                  Youtube Summaries!
                </span>
              </h1>
              <p className="text-lg text-gray-300 mb-8">
                Enter any YouTube URL below and get a concise, human-like
                summary in seconds.
              </p>

              <form
                onSubmit={handleSubmit}
                className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-4 max-w-2xl mx-auto"
              >
                <YouTubeUrlInput />
              </form>
              <ErrorDisplay />
            </div>
          </div>
        </section>
        <NobotSummary />
      </main>

      {/* Two-column layout */}
      <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
        {/* Left column - Form controls and video player */}
        <div className="lg:col-span-6 space-y-6">
          {/* Video Preview/Player - Always present to prevent flickering */}
          <div className="">
            <VideoPreview />
          </div>

          {/* Video Thumbnails */}
          <div className="">
            <VideoThumbnails onThumbnailClick={handleThumbnailClick} />
          </div>

          {/* Model selector - Always present */}
          <div className="space-y-6">
            {/* <ModelSelector /> */}
            {/* <InfoSection /> */}
          </div>

          {/* Logs section - Always present but conditionally enabled */}
          {/* <LogsButton /> */}

          {/* Only show logs in left column */}
          <LogDisplay logs={logs} visible={showLogs} />
        </div>

        {/* Right column - Results */}
        <div className="lg:col-span-6">
          <TabsContainer />
        </div>
      </div>

      {/* Transcript is now displayed in the tabs */}
    </div>
  );
}

// Main component that provides the context
export default function YouTubeForm2() {
  return (
    <FormProvider>
      <YouTubeFormContent />
    </FormProvider>
  );
}
