"use client";

import React, { useState } from "react";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import {
  ThumbsUp,
  ThumbsDown,
  Copy,
  Share2,
  DownloadCloud,
  Bookmark,
  LayoutGrid,
  Columns2,
  FileText,
  Download,
} from "lucide-react";
import VideoPreview2 from "./VideoPreview2";
import MarkdownRenderer from "../MarkdownRenderer";
import { useFormContext } from "./FormContext";
import ChatInterface from "./ChatInterface";
import QnAInterface from "./QnAInterface";
import NobotSummary2Column from "./NobotSummary2Column";
import { copyToClipboard, downloadAsPDF, downloadAsDOC } from '@/lib/utils';

const NobotSummary = () => {
  const [showSummary] = useState(true);
  const [activeTab, setActiveTab] = useState<"bullet" | "chat" | "qna" | "transcript">(
    "bullet"
  );


  const { post, loading, transcript, layoutMode, setLayoutMode, chatMessages, qnaContent, videoTitle } = useFormContext();

  // Get current tab content for copy/download
  const getCurrentContent = () => {
    switch (activeTab) {
      case 'bullet':
        return post || '';
      case 'transcript':
        return transcript || '';
      case 'chat':
        return chatMessages.map(msg => `${msg.role === 'user' ? 'You' : 'Assistant'}: ${msg.content}`).join('\n\n') || '';
      case 'qna':
        return qnaContent || '';
      default:
        return '';
    }
  };

  const getContentTitle = () => {
    const baseTitle = videoTitle || 'YouTube Video';
    switch (activeTab) {
      case 'bullet':
        return `${baseTitle} - Summary`;
      case 'transcript':
        return `${baseTitle} - Transcript`;
      case 'chat':
        return `${baseTitle} - Chat History`;
      case 'qna':
        return `${baseTitle} - Q&A`;
      default:
        return baseTitle;
    }
  };

  const handleCopy = async () => {
    const content = getCurrentContent();
    if (content) {
      const success = await copyToClipboard(content);
      if (success) {
        // You could add a toast notification here
        console.log('Content copied to clipboard');
      }
    }
  };

  const handleDownloadPDF = () => {
    const content = getCurrentContent();
    const title = getContentTitle();
    if (content) {
      downloadAsPDF(content, title);
    }
  };

  const handleDownloadDOC = () => {
    const content = getCurrentContent();
    const title = getContentTitle();
    if (content) {
      downloadAsDOC(content, `${title}.doc`);
    }
  };

  // If 2-column layout is selected, render the 2-column component
  if (layoutMode === 'two-column') {
    return <NobotSummary2Column />;
  }

  return (
    <div className="w-full px-2 sm:px-4">
      {/* Summary Section */}
      {showSummary && (
        <div className="w-full max-w-5xl mx-auto">
          <div className="w-full rounded-xl shadow-xl border border-gray-800 bg-gray-800 p-4 sm:p-6 md:p-8">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-2">
                <div className="h-8 w-8 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white font-bold text-sm">
                  {/* <Bot width={20} height={20} /> */}N
                </div>
                <h3 className="font-space font-bold text-xl">Summary</h3>
              </div>

              {/* Layout Toggle */}
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-400 hidden sm:inline">Layout:</span>
                <div className="flex bg-gray-700 rounded-md overflow-hidden">
                  <button
                    onClick={() => setLayoutMode('single')}
                    className={`p-2 transition-colors ${
                      layoutMode === 'single'
                        ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white'
                        : 'text-gray-300 hover:bg-gray-600'
                    }`}
                    title="Single Column Layout"
                  >
                    <LayoutGrid className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => setLayoutMode('two-column' as 'single' | 'two-column')}
                    className={`p-2 transition-colors ${
                      (layoutMode as string) === 'two-column'
                        ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white'
                        : 'text-gray-300 hover:bg-gray-600'
                    }`}
                    title="Two Column Layout"
                  >
                    <Columns2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>

            {false ? (
              <div className="space-y-6">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="w-full md:w-1/3 h-40 bg-gray-700 rounded animate-pulse" />
                  <div className="space-y-2 flex-1">
                    <div className="h-6 bg-gray-700 rounded animate-pulse w-3/4" />
                    <div className="h-4 bg-gray-700 rounded animate-pulse w-full" />
                    <div className="h-4 bg-gray-700 rounded animate-pulse w-full" />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="h-6 bg-gray-700 rounded animate-pulse w-1/2" />
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-700 rounded animate-pulse w-full" />
                    <div className="h-4 bg-gray-700 rounded animate-pulse w-full" />
                    <div className="h-4 bg-gray-700 rounded animate-pulse w-full" />
                  </div>
                </div>
              </div>
            ) : (
              <>
                <div className="flex flex-col md:flex-row gap-6 mb-6 md:mb-8">
                  <VideoPreview2 />
                </div>

                <div className="p-2 sm:p-3 rounded-lg mb-6 flex flex-col sm:flex-row gap-3 sm:gap-4 items-start sm:items-center justify-between bg-gray-800">
                  <div className="flex flex-wrap gap-1.5 w-full sm:w-auto">
                    <button
                      className={`px-3 py-1.5 text-sm rounded-md ${
                        activeTab === "bullet"
                          ? "bg-gray-700 shadow-sm text-white"
                          : "text-gray-300 hover:text-white"
                      }`}
                      onClick={() => setActiveTab("bullet")}
                    >
                      Smart Summary
                    </button>
                    <button
                      className={`px-3 py-1.5 text-sm rounded-md ${
                        activeTab === "chat"
                          ? "bg-gray-700 shadow-sm text-white"
                          : "text-gray-300 hover:text-white"
                      }`}
                      onClick={() => setActiveTab("chat")}
                      disabled={!transcript}
                    >
                      Chat
                    </button>
                    <button
                      className={`px-3 py-1.5 text-sm rounded-md ${
                        activeTab === "qna"
                          ? "bg-gray-700 shadow-sm text-white"
                          : "text-gray-300 hover:text-white"
                      }`}
                      onClick={() => setActiveTab("qna")}
                      disabled={!transcript}
                    >
                      Q&A
                    </button>
                    <button
                      className={`px-3 py-1.5 text-sm rounded-md ${
                        activeTab === "transcript"
                          ? "bg-gray-700 shadow-sm text-white"
                          : "text-gray-300 hover:text-white"
                      }`}
                      onClick={() => setActiveTab("transcript")}
                      disabled={!transcript}
                    >
                      Transcript
                    </button>
                  </div>

                  {/* Copy/Download Icons */}
                  <div className="flex items-center space-x-2">
                    {/* Copy Button */}
                    <button
                      onClick={handleCopy}
                      className="p-2 rounded-lg text-gray-400 hover:text-gray-200 hover:bg-gray-700 transition-colors"
                      title="Copy to clipboard"
                    >
                      <Copy size={16} />
                    </button>

                    {/* Download PDF Button */}
                    <button
                      onClick={handleDownloadPDF}
                      className="p-2 rounded-lg text-gray-400 hover:text-gray-200 hover:bg-gray-700 transition-colors"
                      title="Download as PDF"
                    >
                      <FileText size={16} />
                    </button>

                    {/* Download DOC Button */}
                    <button
                      onClick={handleDownloadDOC}
                      className="p-2 rounded-lg text-gray-400 hover:text-gray-200 hover:bg-gray-700 transition-colors"
                      title="Download as DOC"
                    >
                      <Download size={16} />
                    </button>
                  </div>
                </div>
                {loading ? (
                  <div className="space-y-4">
                    <div className="h-6 bg-gray-700 rounded animate-pulse w-1/2" />
                    <div className="space-y-2">
                      <div className="h-4 bg-gray-700 rounded animate-pulse w-full" />
                      <div className="h-4 bg-gray-700 rounded animate-pulse w-full" />
                      <div className="h-4 bg-gray-700 rounded animate-pulse w-full" />
                      <div className="h-4 bg-gray-700 rounded animate-pulse w-full" />
                      &nbsp;
                      <div className="h-4 bg-gray-700 rounded animate-pulse w-full" />
                      <div className="h-4 bg-gray-700 rounded animate-pulse w-full" />
                      <div className="h-4 bg-gray-700 rounded animate-pulse w-full" />
                      <div className="h-4 bg-gray-700 rounded animate-pulse w-full" />
                    </div>
                  </div>
                ) : (
                  <>
                    <div className="space-y-6 sm:space-y-8 mb-6 sm:mb-8">
                      {activeTab === "bullet" && (
                        <MarkdownRenderer
                          className="max-h-[500px] overflow-y-auto text-white"
                          content={post || ""}
                        />
                      )}

                      {activeTab === "chat" && (
                        <ChatInterface />
                      )}

                      {activeTab === "qna" && (
                        <QnAInterface />
                      )}

                      {activeTab === "transcript" && (
                        <div className="p-6 rounded-lg bg-gray-800 max-h-[500px] overflow-y-auto">
                          {transcript ? (
                            <div className="space-y-4">
                              {transcript.split('\n\n').map((segment, index) => {
                                const lines = segment.split('\n');
                                const timestamp = lines[0];
                                const text = lines.slice(1).join('\n');

                                // Check if first line looks like a timestamp (contains : and numbers)
                                const isTimestamp = /^\d{1,2}:\d{2}/.test(timestamp);

                                if (isTimestamp && text) {
                                  return (
                                    <div key={index} className="group">
                                      <div className="text-blue-400 text-sm font-mono mb-1">
                                        {timestamp}
                                      </div>
                                      <div className="text-gray-300 leading-relaxed">
                                        {text}
                                      </div>
                                    </div>
                                  );
                                } else {
                                  // Fallback for segments without timestamps
                                  return (
                                    <div key={index} className="text-gray-300 leading-relaxed">
                                      {segment}
                                    </div>
                                  );
                                }
                              })}
                            </div>
                          ) : (
                            <p className="text-gray-400 text-center">No transcript available yet.</p>
                          )}
                        </div>
                      )}
                    </div>

                    {/* Action buttons */}
                    <div className="w-full flex flex-col sm:flex-row justify-between items-start sm:items-center pt-4 sm:pt-6 border-t border-gray-700 gap-3 sm:gap-4">
                      <div className="flex flex-wrap items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="border-purple-500 text-purple-400 hover:bg-purple-500/10 hover:text-purple-300"
                        >
                          <Copy className="h-3.5 w-3.5 mr-1" />
                          Copy
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="border-purple-500 text-purple-400 hover:bg-purple-500/10 hover:text-purple-300"
                        >
                          <Share2 className="h-3.5 w-3.5 mr-1" />
                          Share
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="border-pink-500 text-pink-400 hover:bg-pink-500/10 hover:text-pink-300"
                        >
                          <DownloadCloud className="h-3.5 w-3.5 mr-1" />
                          Export
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="border-pink-500 text-pink-400 hover:bg-pink-500/10 hover:text-pink-300"
                        >
                          <Bookmark className="h-3.5 w-3.5 mr-1" />
                          Save
                        </Button>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-xs text-gray-400">
                          Was this summary helpful?
                        </span>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0 text-gray-400 hover:text-green-400"
                        >
                          <ThumbsUp className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0 text-gray-400 hover:text-pink-400"
                        >
                          <ThumbsDown className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </>
                )}
              </>
            )}
          </div>
        </div>
      )}

      {/* Popular Summaries Section */}
      <div className="w-full max-w-4xl mx-auto mt-8 sm:mt-12 px-2 sm:px-0">
        <div className="flex items-center flex-wrap justify-between mb-6">
          <h2 className="text-xl font-space font-bold">Popular Summaries</h2>
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="lucide lucide-filter w-4 h-4 text-gray-400"
              >
                <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
              </svg>
              <span className="text-sm text-gray-400">Filter by:</span>
            </div>
            <div className="flex gap-2 p-1 rounded-lg bg-gray-800">
              <button className="px-3 py-1 text-xs rounded-md transition-colors bg-gradient-to-r from-purple-500 to-pink-500 text-white">
                All
              </button>
              <button className="px-3 py-1 text-xs rounded-md transition-colors text-gray-400 hover:bg-gray-700 hover:text-white">
                Tech
              </button>
              <button className="px-3 py-1 text-xs rounded-md transition-colors text-gray-400 hover:bg-gray-700 hover:text-white">
                News
              </button>
              <button className="px-3 py-1 text-xs rounded-md transition-colors text-gray-400 hover:bg-gray-700 hover:text-white">
                Science
              </button>
              <button className="px-3 py-1 text-xs rounded-md transition-colors text-gray-400 hover:bg-gray-700 hover:text-white">
                Entertainment
              </button>
            </div>
          </div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
          {[
            {
              title:
                "Trevor Noah: My Depression Was Linked To ADHD! Why I Left The Daily Show!",
              thumbnail:
                "https://i3.ytimg.com/vi/yGLzpt3caXA/maxresdefault.jpg",
              category: "entertainment",
            },
            {
              title:
                "How ChatGPT Works: The Architecture Behind the Revolution",
              thumbnail:
                "https://i3.ytimg.com/vi/bZQun8Y4L2A/maxresdefault.jpg",
              category: "tech",
            },
            {
              title: "The Science of Sleep: How to Get Better Rest",
              thumbnail:
                "https://i3.ytimg.com/vi/gbQFSMayJxk/maxresdefault.jpg",
              category: "science",
            },
            {
              title: "Global Economy Update: Latest Trends and Forecasts",
              thumbnail:
                "https://i3.ytimg.com/vi/dZL25NSLhEA/maxresdefault.jpg",
              category: "news",
            },
          ].map((item, index) => (
            <div
              key={index}
              className="rounded-lg overflow-hidden bg-gray-800 hover:bg-gray-700 transition-all cursor-pointer shadow-lg"
              style={{
                background: "linear-gradient(145deg, #1f2937 0%, #111827 100%)",
                border: "1px solid #374151",
              }}
              onClick={() => {
              }}
            >
              <div className="relative">
                <Image
                  src={item.thumbnail}
                  alt={item.title}
                  width={320}
                  height={192}
                  className="w-full h-48 object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent flex items-end p-4">
                  <span
                    className={`text-xs px-2 py-1 rounded-full ${
                      item.category === "tech"
                        ? "bg-gradient-to-r from-purple-500 to-pink-500 text-white"
                        : item.category === "science"
                        ? "bg-gradient-to-r from-purple-400 to-blue-500 text-white"
                        : item.category === "entertainment"
                        ? "bg-gradient-to-r from-pink-500 to-red-500 text-white"
                        : "bg-gradient-to-r from-blue-500 to-cyan-500 text-white"
                    }`}
                  >
                    {item.category.charAt(0).toUpperCase() +
                      item.category.slice(1)}
                  </span>
                </div>
              </div>
              <div className="p-4">
                <h3 className="font-medium text-sm line-clamp-2">
                  {item.title}
                </h3>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default NobotSummary;
