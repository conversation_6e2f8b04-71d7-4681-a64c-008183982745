'use client';

export default function InfoSection() {
  return (
    <div className="space-y-2">
      <p className="text-xs text-gray-500 mt-1">
        <span className="block mt-1 text-xs text-green-600 font-medium">
          Using latest OpenAI models for transcription and intelligent summary generation.
        </span>
        <span className="block mt-1 text-xs text-green-600 font-medium">
          Summary length adapts automatically based on content complexity and importance.
        </span>
        <span className="block mt-1 text-xs text-green-600 font-medium">
          Includes both narrative overview and bullet points with rich context and maximum value.
        </span>
      </p>
    </div>
  );
}
