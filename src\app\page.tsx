"use client";

import <PERSON>sco<PERSON> from "@/components/Mascot";
import { Bo<PERSON> } from "lucide-react";
import { useState } from "react";
import { useRouter } from "next/navigation";

// SUpabase databse pass
// 5Gf2a8Yy+5M*.9H - for shivam's Project
// jG9NYyAHG9oMXgyL - for nobot project
// 2oDXSNbKRgJBkEiy - for shivam's org
// QXaJJjWde8hYtnhc - for shivam's org -> nobot project

const Index = () => {
  const [url1, setUrl1] = useState("");
  const [url2, setUrl2] = useState("");
  const [loading1, setLoading1] = useState(false);
  const [loading2, setLoading2] = useState(false);
  const router = useRouter();

  const handleSubmit1 = async (e: React.FormEvent) => {
    e.preventDefault();
    if (url1.trim()) {
      setLoading1(true);
      // Add a small delay to show the loading state
      await new Promise(resolve => setTimeout(resolve, 500));
      router.push(`/summary?url=${encodeURIComponent(url1.trim())}`);
    }
  };

  const handleSubmit2 = async (e: React.FormEvent) => {
    e.preventDefault();
    if (url2.trim()) {
      setLoading2(true);
      // Add a small delay to show the loading state
      await new Promise(resolve => setTimeout(resolve, 500));
      router.push(`/summary?url=${encodeURIComponent(url2.trim())}`);
    }
  };
  return (
    <>
      {/* Hero Section */}
      <section className="max-w-7xl mx-auto px-6 py-0 md:py-20 text-center">
        <div className="relative">
          <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
            AI-Powered Summaries for{" "}
            <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              YouTube Videos
            </span>
          </h1>

          <div className="absolute -top-4 -right-4 md:-right-8">
            <div className="w-16 h-16 flex items-center justify-center">
              <Mascot size="lg" expression="thinking" />
            </div>
          </div>
        </div>

        <p className="text-xl text-gray-300 mb-12 max-w-2xl mx-auto leading-relaxed">
          Nobot gives you instant, human-like summaries of any YouTube video —
          so you get the gist without the grind.
        </p>

        <form onSubmit={handleSubmit1} className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-4 max-w-2xl mx-auto">
          <input
            type="text"
            value={url1}
            onChange={(e) => setUrl1(e.target.value)}
            placeholder="Paste YouTube URL here"
            className="flex-1 bg-gray-800 border border-gray-700 rounded-lg px-6 py-4 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent w-full sm:w-auto"
            disabled={loading1}
          />
          <button
            type="submit"
            disabled={loading1}
            className="bg-gradient-to-r from-purple-500 to-pink-500 px-8 py-4 rounded-lg font-bold text-lg hover:from-purple-600 hover:to-pink-600 transition-all transform hover:scale-105 w-full sm:w-auto disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
          >
            {loading1 ? (
              <div className="flex items-center justify-center">
                <svg
                  className="animate-spin h-5 w-5 mr-2"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Redirecting...
              </div>
            ) : (
              "Summarize It"
            )}
          </button>
        </form>

        <div className="flex items-center justify-center space-x-8 mt-8 text-sm text-gray-400">
          <div className="flex items-center">
            <span className="text-pink-500 mr-2 text-[20px]">•</span>No sign-up
            required
          </div>
          <div className="flex items-center">
            <span className="text-green-500 mr-2 text-[20px]">•</span>2 free
            summaries daily
          </div>
          <div className="flex items-center">
            <span className="text-purple-500 mr-2 text-[20px]">•</span>
            Human-like summaries
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section id="how-it-works" className="max-w-7xl mx-auto px-6 py-20">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-4">How It Works</h2>
          <p className="text-gray-300 text-lg">
            Getting insights from videos has never been easier. Just three
            simple steps.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          <div className="bg-gray-800 p-8 rounded-2xl text-center hover:bg-gray-750 transition-colors">
            <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-2xl">🔗</span>
            </div>
            <h3 className="text-xl font-bold mb-4">Paste URL</h3>
            <p className="text-gray-300">
              Copy any YouTube video URL and paste it into Nobot.
            </p>
          </div>

          <div className="bg-gray-800 p-8 rounded-2xl text-center hover:bg-gray-750 transition-colors">
            <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-2xl">⚡</span>
            </div>
            <h3 className="text-xl font-bold mb-4">Get Summary</h3>
            <p className="text-gray-300">
              Our AI instantly analyzes and summarizes the content for you.
            </p>
          </div>

          <div className="bg-gray-800 p-8 rounded-2xl text-center hover:bg-gray-750 transition-colors">
            <div className="w-16 h-16 bg-gradient-to-r from-pink-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-2xl">🧠</span>
            </div>
            <h3 className="text-xl font-bold mb-4">Stay Smart</h3>
            <p className="text-gray-300">
              Read the key points in seconds, instead of watching for hours.
            </p>
          </div>
        </div>
      </section>

      {/* Why Nobot */}
      <section id="why-nobot" className="max-w-7xl mx-auto px-6 py-20">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-4">Why Nobot?</h2>
          <p className="text-gray-300 text-lg">
            Because your time is precious. Nobot helps you consume more content
            in less time, without sacrificing the quality of information.
          </p>

          <div className="absolute right-8 top-8">
            <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-8">
              <Bot width={30} height={30} />
            </div>
          </div>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          <div className="bg-gray-800 p-8 rounded-2xl">
            <h3 className="text-2xl font-bold mb-4">Lightning Fast</h3>
            <p className="text-gray-300 mb-4">
              Get summaries in seconds, no matter how long the video is.
            </p>
            <div className="flex items-center space-x-4">
              <span className="text-purple-400 font-bold">8hr video</span>
              <span className="text-gray-400">→</span>
              <span className="text-pink-400 font-bold">90 sec read</span>
            </div>
          </div>

          <div className="bg-gray-800 p-8 rounded-2xl">
            <h3 className="text-2xl font-bold mb-4">Skip The Fluff</h3>
            <p className="text-gray-300 mb-4">
              No more sitting through long intros or off-topic tangents.
            </p>
            <div className="flex items-center space-x-4">
              <span className="text-purple-400 font-bold">3 min intro</span>
              <span className="text-gray-400">→</span>
              <span className="text-pink-400 font-bold">0 sec with Nobot</span>
            </div>
          </div>

          <div className="bg-gray-800 p-8 rounded-2xl">
            <h3 className="text-2xl font-bold mb-4">Human-Like Quality</h3>
            <p className="text-gray-300 mb-4">
              Our AI creates summaries that feel written by a smart friend.
            </p>
            <div className="flex items-center space-x-4">
              <span className="text-purple-400 font-bold">Robot speak</span>
              <span className="text-gray-400">→</span>
              <span className="text-pink-400 font-bold">Human insights</span>
            </div>
          </div>

          <div className="bg-gray-800 p-8 rounded-2xl">
            <h3 className="text-2xl font-bold mb-4">Key Points Only</h3>
            <p className="text-gray-300 mb-4">
              Focus on what matters with organized, concise bullet points.
            </p>
            <div className="flex items-center space-x-4">
              <span className="text-purple-400 font-bold">Rambling</span>
              <span className="text-gray-400">→</span>
              <span className="text-pink-400 font-bold">Structured points</span>
            </div>
          </div>
        </div>
      </section>

      {/* User Love / Testimonials */}
      <section id="testimonials" className="max-w-7xl mx-auto px-6 py-20">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-4">User Love</h2>
          <p className="text-gray-300 text-lg">
            Don&apos;t just take our word for it. See what our users are saying about
            Nobot.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          <div className="bg-gray-800 p-8 rounded-2xl">
            <p className="text-gray-300 mb-6 italic">
              &ldquo;Nobot helped me ace my exams by summarizing 3 hour lectures in
              minutes. Total game changer for students!&rdquo;
            </p>
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                <span className="text-sm font-bold">AK</span>
              </div>
              <div>
                <p className="font-bold">Alex Kim</p>
                <p className="text-gray-400 text-sm">
                  Computer Science Student
                </p>
              </div>
            </div>
          </div>

          <div className="bg-gray-800 p-8 rounded-2xl">
            <p className="text-gray-300 mb-6 italic">
              &ldquo;As a content creator, I use Nobot to keep up with industry trends
              without spending hours watching videos.&rdquo;
            </p>
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center">
                <span className="text-sm font-bold">MJ</span>
              </div>
              <div>
                <p className="font-bold">Maya Johnson</p>
                <p className="text-gray-400 text-sm">YouTube Creator</p>
              </div>
            </div>
          </div>

          <div className="bg-gray-800 p-8 rounded-2xl">
            <p className="text-gray-300 mb-6 italic">
              &ldquo;I&apos;m all about productivity hacks, and Nobot is one of the best
              I&apos;ve found. I get through 3x more content now.&rdquo;
            </p>
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-pink-500 to-red-500 rounded-full flex items-center justify-center">
                <span className="text-sm font-bold">TM</span>
              </div>
              <div>
                <p className="font-bold">Theo Martinez</p>
                <p className="text-gray-400 text-sm">Product Manager</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA */}
      <section className="max-w-7xl mx-auto px-6 py-20 text-center">
        <div className="relative">
          <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-8">
            <Bot width={30} height={30} />
          </div>

          <h2 className="text-4xl font-bold mb-6">
            Ready to save hours of your time?
          </h2>
          <p className="text-gray-300 text-lg mb-8 max-w-2xl mx-auto">
            Paste any YouTube URL below and see the magic happen. No sign-up
            required to try your first 5 summaries.
          </p>

          <form onSubmit={handleSubmit2} className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-4 max-w-2xl mx-auto">
            <input
              type="text"
              value={url2}
              onChange={(e) => setUrl2(e.target.value)}
              placeholder="Paste YouTube URL here"
              className="flex-1 bg-gray-800 border border-gray-700 rounded-lg px-6 py-4 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent w-full sm:w-auto"
              disabled={loading2}
            />
            <button
              type="submit"
              disabled={loading2}
              className="bg-gradient-to-r from-purple-500 to-pink-500 px-8 py-4 rounded-lg font-bold text-lg hover:from-purple-600 hover:to-pink-600 transition-all transform hover:scale-105 w-full sm:w-auto disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
            >
              {loading2 ? (
                <div className="flex items-center justify-center">
                  <svg
                    className="animate-spin h-5 w-5 mr-2"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Redirecting...
                </div>
              ) : (
                "Summarize It"
              )}
            </button>
          </form>
        </div>
      </section>
    </>
  );
};

export default Index;
