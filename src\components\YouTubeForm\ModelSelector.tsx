'use client';

export default function ModelSelector() {
  // Since we're using a simple constant for model selection,
  // this component now shows the currently selected model as read-only information

  return (
    <div className="space-y-2">
      <label className="block text-sm font-medium">
        AI Model
      </label>
      <div className="p-3 rounded-md border border-primary bg-primary/5">
        <div className="flex items-center justify-between">
          <div className="font-medium">🤖 GPT-4o Mini</div>
          <div className="h-4 w-4 rounded-full bg-primary"></div>
        </div>
        <div className="text-xs mt-1 text-muted-foreground">Currently Selected Model</div>
        <div className="text-xs mt-1">Optimized balance of quality and speed</div>
      </div>
      <p className="text-xs text-muted-foreground mt-1">
        GPT-4o Mini provides excellent quality summaries with fast processing times.
      </p>
    </div>
  );
}
