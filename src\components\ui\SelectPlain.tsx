'use client';

import React from 'react';

interface SelectOption {
  value: string;
  label: string;
}

interface SelectProps {
  id: string;
  value: string;
  onChange: (value: string) => void;
  options: SelectOption[];
  title?: string;
  className?: string;
}

export default function Select({
  id,
  value,
  onChange,
  options,
  title,
  className = '',
}: SelectProps) {
  return (
    <select
      id={id}
      value={value}
      onChange={(e) => onChange(e.target.value)}
      className={`text-xs bg-gray-700 text-white rounded-md px-2 py-1 border border-gray-600 focus:border-purple-500 focus:ring-1 focus:ring-purple-500 outline-none ${className}`}
      title={title}
      style={{
        // fontFamily: 'var(--font-inter)',
        fontSize: '14px',
        // fontStyle: 'normal',
        // fontWeight: 400,
        // letterSpacing: '-0.08px',
        // lineHeight: '22px'
      }}
    >
      {options.map((option) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  );
}
