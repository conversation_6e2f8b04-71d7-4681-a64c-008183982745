"use client";

import React from "react";
import { FormProvider, useFormContext } from "./FormContext";
import YouTubeUrlInput from "./YouTubeUrlInput";
// import ModelSelector from './ModelSelector';
import ErrorDisplay from "./ErrorDisplay";
// import LogsButton from './LogsButton';
import { dummyThumbnails } from "./VideoThumbnails";
// Icons are imported but not used in this file
import NobotSummary from "./NobotSummary";
import LanguageSelector from "./LanguageSelector";
import StyleSelector from "./StyleSelector";
import SizeSelector from "./SizeSelector";
// import InfoSection from './InfoSection';

// Form component that uses the context
function YouTubeFormContent() {
  const {
    handleSubmit,
    setVideoTitle,
    setVideoThumbnail,
    setVideoId,
    setPost,
    videoId,
  } = useFormContext();



  // Set default video preview on component mount if no video is selected
  React.useEffect(() => {
    if (!videoId) {
      setVideoTitle(dummyThumbnails[1].title);
      setVideoThumbnail(dummyThumbnails[1].url);
      setVideoId(dummyThumbnails[1].id.toString());
      setPost(dummyThumbnails[1].summary);
    }
  }, [setVideoTitle, setVideoThumbnail, setVideoId, setPost, videoId]);

  return (
    <div className="w-full bg-gray-900 text-white">
      {/* <LogDisplay logs={logs} visible={true} /> */}
      <main className="w-full">
        <section className="pt-12 sm:pt-16 pb-0 md:pb-4 sm:pb-16 w-full relative hero-gradient">
          <div className="w-full max-w-7xl mx-auto px-2 sm:px-4">
            <div className="w-full text-center mb-8 sm:mb-10 px-2">
              {/* <div className="inline-block p-2 rounded-lg bg-purple-500/10 mb-4">
                <BookOpen className="w-6 h-6 text-purple-400" />
              </div> */}
              <h1 className="text-4xl sm:text-5xl md:text-7xl font-bold mb-4 sm:mb-6 leading-tight">
                Summarize YouTube{" "}
                <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                  Videos with AI
                </span>
              </h1>

              {/* <h1 className="text-4xl sm:text-5xl md:text-7xl font-bold mb-4 sm:mb-6 leading-tight">
                Get Instant{" "}
                <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                  Youtube Summaries!
                </span>
              </h1> */}
              <p className="text-base sm:text-lg text-gray-300 mb-6 sm:mb-8 max-w-3xl mx-auto">
                Enter any YouTube URL below and get a concise, human-like
                summary in seconds.
              </p>

              <form
                onSubmit={handleSubmit}
                className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-4 w-full max-w-2xl mx-auto"
              >
                <YouTubeUrlInput />
              </form>
              <ErrorDisplay />
              
              <div className="flex flex-wrap md:flex-nowrap justify-between mt-8 mx-auto max-w-2xl gap-8">
                
                <div className="flex gap-2 w-72 md:w-auto">
                {/* Language Selector */}
                <LanguageSelector />

                {/* Summary Style Selector */}
                <StyleSelector />
                </div>
                {/* length */}
                <SizeSelector />
              </div>
            </div>
          </div>
        </section>
        <NobotSummary />
      </main>

      {/* Two-column layout */}
      {/* <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 hidden">
        <div className="lg:col-span-6 space-y-6">
          <div className="">
            <VideoPreview />
          </div>

          <div className="">
            <VideoThumbnails onThumbnailClick={handleThumbnailClick} />
          </div>


          <LogDisplay logs={logs} visible={showLogs} />
        </div>

        <div className="lg:col-span-6">
          <TabsContainer />
        </div>
      </div> */}
    </div>
  );
}

// Main component that provides the context
export default function YouTubeForm2() {
  return (
    <FormProvider>
      <YouTubeFormContent />
    </FormProvider>
  );
}
