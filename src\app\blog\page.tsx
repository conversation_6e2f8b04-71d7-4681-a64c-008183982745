
import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { CalendarDays, Clock, ArrowRight } from 'lucide-react';
import { blogPosts, type BlogPost } from '@/lib/blog-data';

const Blog = () => {
  return (
    <div className="min-h-screen bg-gray-900 text-white">

      {/* Hero Section */}
      <div className="relative">
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-96 h-96 bg-purple-600/20 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 right-0 w-80 h-80 bg-pink-600/20 rounded-full blur-3xl"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-blue-600/10 rounded-full blur-3xl"></div>
        </div>
        
        <div className="relative z-10 text-center py-20 px-6">
          <h1 className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-purple-400 via-pink-400 to-blue-400 bg-clip-text text-transparent">
            Blog & Insights
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Explore the latest trends, insights, and innovations in AI technology, productivity, and digital transformation
          </p>
          <div className="mt-8 flex items-center justify-center space-x-4 text-sm text-gray-400">
            <span className="bg-purple-500/10 px-3 py-1 rounded-full">Weekly Updates</span>
            <span className="bg-pink-500/10 px-3 py-1 rounded-full">Expert Insights</span>
            <span className="bg-blue-500/10 px-3 py-1 rounded-full">In-Depth Analysis</span>
          </div>
        </div>
      </div>

      {/* Featured Post */}
      <div className="max-w-7xl mx-auto px-6 mb-16">
        <div className="bg-gradient-to-r from-purple-900/50 to-pink-900/50 rounded-2xl overflow-hidden backdrop-blur-xl border border-purple-500/20">
          <div className="grid md:grid-cols-2 gap-0">
            <div className="relative h-64 md:h-auto">
              <Image
                src={blogPosts[0].image}
                alt={blogPosts[0].title}
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, 50vw"
              />
              <div className="absolute top-4 left-4">
                <span className="bg-yellow-500 text-black px-3 py-1 rounded-full text-xs font-medium">
                  Featured
                </span>
              </div>
            </div>
            <div className="p-8 flex flex-col justify-center">
              <div className="flex items-center space-x-4 text-sm text-gray-400 mb-4">
                <span className="bg-purple-500/20 text-purple-300 px-3 py-1 rounded-full">
                  {blogPosts[0].category}
                </span>
                <div className="flex items-center space-x-1">
                  <CalendarDays className="w-4 h-4" />
                  <span>{new Date(blogPosts[0].date).toLocaleDateString()}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Clock className="w-4 h-4" />
                  <span>{blogPosts[0].readTime}</span>
                </div>
              </div>
              <h2 className="text-2xl md:text-3xl font-bold mb-4 text-white">
                {blogPosts[0].title}
              </h2>
              <p className="text-gray-300 mb-6 leading-relaxed">
                {blogPosts[0].excerpt}
              </p>
              <Link
                href={`/blog/${blogPosts[0].id}`}
                className="inline-flex items-center text-purple-400 hover:text-purple-300 transition-colors font-medium"
              >
                Read the full article
                <ArrowRight className="w-4 h-4 ml-2" />
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Blog Posts Grid */}
      <div className="max-w-7xl mx-auto px-6 pb-20">
        <h2 className="text-3xl font-bold mb-12 text-center">Latest Articles</h2>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {blogPosts.slice(1).map((post: BlogPost) => (
            <Card key={post.id} className="bg-gray-800/30 backdrop-blur-xl border-gray-700 hover:border-purple-500/30 transition-all duration-300 group overflow-hidden">
              <div className="relative h-48 overflow-hidden">
                <Image
                  src={post.image}
                  alt={post.title}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-gray-900/80 to-transparent"></div>
                <div className="absolute bottom-4 left-4">
                  <span className="bg-purple-500/20 backdrop-blur-sm text-purple-300 px-3 py-1 rounded-full text-xs">
                    {post.category}
                  </span>
                </div>
              </div>
              
              <CardHeader className="pb-4">
                <div className="flex items-center space-x-4 text-sm text-gray-400 mb-3">
                  <div className="flex items-center space-x-1">
                    <CalendarDays className="w-3 h-3" />
                    <span>{new Date(post.date).toLocaleDateString()}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Clock className="w-3 h-3" />
                    <span>{post.readTime}</span>
                  </div>
                </div>
                <CardTitle className="text-lg text-white group-hover:text-purple-300 transition-colors line-clamp-2">
                  <Link href={`/blog/${post.id}`}>
                    {post.title}
                  </Link>
                </CardTitle>
                <CardDescription className="text-gray-300 text-sm leading-relaxed line-clamp-3">
                  {post.excerpt}
                </CardDescription>
              </CardHeader>
              
              <CardContent className="pt-0">
                <Link
                  href={`/blog/${post.id}`}
                  className="inline-flex items-center text-purple-400 hover:text-purple-300 transition-colors text-sm font-medium"
                >
                  Continue reading
                  <ArrowRight className="w-3 h-3 ml-1" />
                </Link>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

    </div>
  );
};

export default Blog;
