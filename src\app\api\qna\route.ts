import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';
import { env, validateEnv } from '@/lib/env';

// Validate environment variables
validateEnv();

// Simple constant to define which model to use (same as in actions.ts)
const SELECTED_MODEL = env.GPT_MODEL_MINI;

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: env.OPENAI_API_KEY,
});

export async function POST(request: NextRequest) {
  try {
    const { transcript, videoTitle } = await request.json();

    if (!transcript) {
      return NextResponse.json(
        { error: 'Transcript is required' },
        { status: 400 }
      );
    }

    const prompt = `You are an expert content analyst. Create a comprehensive Q&A format from the following YouTube video transcript. 

Video Title: ${videoTitle || 'Unknown'}

Instructions:
- Generate 8-15 thoughtful questions that cover the main topics, key insights, and important details from the video
- Provide detailed, informative answers based strictly on the transcript content
- Include specific quotes or references from the transcript when relevant
- If timestamps are available in the transcript (e.g., [00:15]), reference them in your answers
- Organize questions from general/overview topics to more specific details
- Use clear, engaging question formats
- Format the output in clean markdown with proper headings and structure
- Make sure each Q&A pair provides real value to someone who wants to understand the video content

Format your response as:

# Q&A: [Video Title]

## Question 1: [Question about main topic/overview]
**Answer:** [Detailed answer with specific references to transcript content]

## Question 2: [Question about key insight]
**Answer:** [Detailed answer with quotes or specific details]

[Continue with more questions...]

---

Video Transcript:
"""
${transcript}
"""`;

    // Call OpenAI API
    const completion = await openai.chat.completions.create({
      model: SELECTED_MODEL,
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ],
      max_tokens: 2000,
      temperature: 0.7,
    });

    const qna = completion.choices[0]?.message?.content;

    if (!qna) {
      throw new Error('No response from OpenAI');
    }

    return NextResponse.json({ qna });

  } catch (error: any) {
    console.error('QnA API error:', error);
    
    // Handle specific OpenAI errors
    if (error.code === 'insufficient_quota') {
      return NextResponse.json(
        { error: 'OpenAI API quota exceeded. Please check your billing settings.' },
        { status: 429 }
      );
    }
    
    if (error.status === 401) {
      return NextResponse.json(
        { error: 'OpenAI API key is invalid or missing.' },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to generate Q&A. Please try again.' },
      { status: 500 }
    );
  }
}
