"use client";

import React, { useState } from "react";
import Image from "next/image";
import { useFormContext } from "./FormContext";
import { Clock, PlayIcon } from "lucide-react";
const theme = "dark";
export default function VideoPreview2() {
  const { videoId, videoTitle, videoThumbnail, videoDuration, videoPublishDate } = useFormContext();
  const [isPlaying, setIsPlaying] = useState(false);

  // Reset playing state when videoId changes
  React.useEffect(() => {
    setIsPlaying(false);
  }, [videoId]);

  // Function to start playing the video
  const playVideo = () => {
    setIsPlaying(true);
  };

  return (
    <>
      <div className="w-full md:w-1/3 rounded-lg overflow-hidden relative group aspect-video md:aspect-auto">
        {!isPlaying ? (
          <>
            <div className="w-full h-full md:h-48 relative">
              <Image
                src={
                  videoThumbnail ||
                  "https://i3.ytimg.com/vi/yGLzpt3caXA/maxresdefault.jpg"
                }
                alt={videoTitle || "Title Not Found"}
                fill
                className="object-cover transition-transform group-hover:scale-105"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              />
            </div>
            <div
              onClick={playVideo}
              className="absolute inset-0 flex items-center justify-center cursor-pointer"
            >
              <div className="w-12 h-12 md:w-14 md:h-14 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center transition-transform transform group-hover:scale-110">
                <div className="border-b-transparent border-l-white border-t-transparent flex items-center">
                  <PlayIcon className="w-5 h-5 md:w-6 md:h-6" fill="white" />
                </div>
              </div>
            </div>
          </>
        ) : (
          <iframe
            className="absolute inset-0 w-full h-full"
            src={`https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0`}
            title={videoTitle || "YouTube video player"}
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
          ></iframe>
        )}
      </div>

      <div className="flex-1 mt-4 md:mt-0">
        <h2 className="text-lg md:text-xl font-space font-bold mb-2 md:mb-3 line-clamp-2">
          {videoTitle || "Title Not Found"}
        </h2>
        <div className="flex flex-wrap items-center gap-x-3 gap-y-1 mb-3 md:mb-4 text-xs md:text-sm">
          <div className="flex items-center gap-1 text-gray-400">
            <Clock className="w-3.5 h-3.5 md:w-4 md:h-4 flex-shrink-0" />
            <span className="text-gray-400 whitespace-nowrap">
              {videoDuration || 'Loading...'}
            </span>
          </div>
          {videoPublishDate && (
            <>
              <div className="h-1 w-1 rounded-full bg-gray-600 hidden sm:block"></div>
              <span className="text-gray-400 whitespace-nowrap">{videoPublishDate}</span>
            </>
          )}
        </div>
        <p
          className={`text-xs sm:text-sm mb-3 md:mb-4 ${
            theme === "dark" ? "text-gray-300" : "text-gray-600"
          }`}
        >
          This summary was generated by Nobot AI from a YouTube video. It
          captures the key points and insights from the content.
        </p>

        <div className="flex flex-wrap gap-1.5 sm:gap-2">
          <span
            className={`text-xs px-2 py-1 rounded-full ${
              theme === "dark"
                ? "bg-pink-500/20 text-pink-500"
                : "bg-pink-500/20 text-pink-500"
            }`}
          >
            Biography
          </span>
          <span
            className={`text-xs px-2 py-1 rounded-full ${
              theme === "dark"
                ? "bg-green-500/20 text-green-500"
                : "bg-green-500/20 text-green-500"
            }`}
          >
            Interview
          </span>
          <span
            className={`text-xs px-2 py-1 rounded-full ${
              theme === "dark"
                ? "bg-purple-500/20 text-purple-500"
                : "bg-purple-500/20 text-purple-500"
            }`}
          >
            Comedy
          </span>
        </div>
      </div>
    </>
  );

  return (
    <div className="rounded-lg overflow-hidden w-full">
      <div className="relative pb-[56.25%] bg-black">
        {!isPlaying ? (
          // Thumbnail with play button overlay
          <>
            {videoThumbnail && (
              <Image
                src={videoThumbnail || "https://i3.ytimg.com/vi/yGLzpt3caXA/maxresdefault.jpg"}
                alt={videoTitle || "YouTube video thumbnail"}
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              />
            )}
            <button
              onClick={playVideo}
              className="absolute inset-0 flex items-center justify-center transition-opacity"
              aria-label="Play video"
            >
              <div className="w-20 h-20 rounded-full bg-black/60 flex items-center justify-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="36"
                  height="36"
                  viewBox="0 0 24 24"
                  fill="white"
                >
                  <polygon points="8 5 19 12 8 19 8 5" />
                </svg>
              </div>
            </button>
          </>
        ) : (
          // YouTube iframe player
          <iframe
            className="absolute inset-0 w-full h-full"
            src={`https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0`}
            title={videoTitle || "YouTube video player"}
            frameBorder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
          ></iframe>
        )}
      </div>
      <div className="p-2 sm:p-3">
        <h3 className="font-medium text-sm sm:text-base line-clamp-2">
          {videoTitle || "Loading video information..."}
        </h3>
      </div>
    </div>
  );
}
