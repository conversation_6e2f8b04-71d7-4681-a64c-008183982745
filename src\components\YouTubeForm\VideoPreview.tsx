'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { useFormContext } from './FormContext';

export default function VideoPreview() {
  const { videoId, videoTitle, videoThumbnail } = useFormContext();
  const [isPlaying, setIsPlaying] = useState(false);

  // Reset playing state when videoId changes
  React.useEffect(() => {
    setIsPlaying(false);
  }, [videoId]);

  // Instead of returning null, show a placeholder when no video is selected
  if (!videoId) {
    return (
      <div className="rounded-lg overflow-hidden shadow-sm bg-gray-50 dark:bg-gray-800">
        <div className="relative pb-[56.25%] bg-gray-100 dark:bg-gray-700">
          <div className="absolute inset-0 flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round" className="text-gray-300 dark:text-gray-600">
              <path d="M2.5 17a24.12 24.12 0 0 1 0-10 2 2 0 0 1 1.4-1.4 49.56 49.56 0 0 1 16.2 0A2 2 0 0 1 21.5 7a24.12 24.12 0 0 1 0 10 2 2 0 0 1-1.4 1.4 49.55 49.55 0 0 1-16.2 0A2 2 0 0 1 2.5 17" />
              <path d="m10 15 5-3-5-3z" />
            </svg>
          </div>
        </div>
        <div className="p-3">
          <div className="flex justify-between items-start">
            <h3 className="font-medium text-lg text-gray-400 dark:text-gray-500">
              No video selected
            </h3>
          </div>
        </div>
      </div>
    );
  }

  // Function to start playing the video
  const playVideo = () => {
    setIsPlaying(true);
  };

  return (
    <div className="rounded-lg overflow-hidden">
      <div className="relative pb-[56.25%] bg-black">
        {!isPlaying ? (
          // Thumbnail with play button overlay
          <>
            {videoThumbnail && (
              <div className="absolute inset-0 w-full h-full">
                <Image
                  src={videoThumbnail}
                  alt={videoTitle || 'YouTube video thumbnail'}
                  className="object-cover"
                  fill
                  sizes="(max-width: 768px) 100vw, 50vw"
                  unoptimized={!videoThumbnail.startsWith('/')}
                />
              </div>
            )}
            <button
              onClick={playVideo}
              className="absolute inset-0 flex items-center justify-center transition-opacity"
              aria-label="Play video"
            >
              <div className="w-20 h-20 rounded-full bg-black/60 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 24 24" fill="white">
                  <polygon points="8 5 19 12 8 19 8 5" />
                </svg>
              </div>
            </button>
          </>
        ) : (
          // YouTube iframe player
          <iframe
            className="absolute inset-0 w-full h-full"
            src={`https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0`}
            title={videoTitle || 'YouTube video player'}
            frameBorder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
          ></iframe>
        )}
      </div>
      <div className="">
        <h3 className="font-medium text-lg line-clamp-2 bg-gray-100 dark:bg-gray-900 px-3 py-2 rounded-md">
          {videoTitle || 'Loading video information...'}
        </h3>
       
      </div>
    </div>
  );
}
