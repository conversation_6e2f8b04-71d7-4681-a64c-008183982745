import React from 'react';
import { cn } from "@/lib/utils";

type MascotProps = {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  expression?: 'happy' | 'thinking' | 'excited';
};

const Mascot = ({ className, size = 'md', expression = 'happy' }: MascotProps) => {
  const sizeClasses = {
    sm: 'w-10 h-10',
    md: 'w-16 h-16',
    lg: 'w-24 h-24',
  };

  const faces = {
    happy: (
      <>
        <circle cx="14" cy="14" r="2.5" fill="white" />
        <circle cx="28" cy="14" r="2.5" fill="white" />
        <path d="M16 24C18.6667 26 23.3333 26 26 24" stroke="white" strokeWidth="2" strokeLinecap="round" />
      </>
    ),
    thinking: (
      <>
        <circle cx="14" cy="14" r="2.5" fill="white" />
        <circle cx="28" cy="14" r="2.5" fill="white" />
        <path d="M16 26H26" stroke="white" strokeWidth="2" strokeLinecap="round" />
        <path d="M34 16C36 14 36 11 34 8" stroke="white" strokeWidth="2" strokeLinecap="round" />
      </>
    ),
    excited: (
      <>
        <circle cx="14" cy="14" r="2.5" fill="white" />
        <circle cx="28" cy="14" r="2.5" fill="white" />
        <path d="M16 24C18.6667 28 23.3333 28 26 24" stroke="white" strokeWidth="2" strokeLinecap="round" />
        <path d="M8 6L12 2" stroke="white" strokeWidth="2" strokeLinecap="round" />
        <path d="M34 6L30 2" stroke="white" strokeWidth="2" strokeLinecap="round" />
      </>
    ),
  };

  return (
    <div className={cn("relative", className)}>
      <svg
        viewBox="0 0 42 42"
        className={cn("animate-bounce", sizeClasses[size])}
        xmlns="http://www.w3.org/2000/svg"
      >
        <defs>
          <linearGradient id="mascotGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#8b5cf6" /> {/* purple-500 */}
            <stop offset="100%" stopColor="#ec4899" /> {/* pink-500 */}
          </linearGradient>
        </defs>

        {/* Body */}
        <rect x="8" y="6" width="26" height="30" rx="13" fill="url(#mascotGradient)" />

        {/* Antenna */}
        <rect x="19" y="1" width="4" height="5" rx="2" fill="url(#mascotGradient)" />
        <circle cx="21" cy="1" r="1" fill="url(#mascotGradient)" />

        {/* Face */}
        {faces[expression]}
      </svg>
    </div>
  );
};

export default Mascot;
