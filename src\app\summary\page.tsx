import { Suspense } from "react";
import YouTubeForm2 from "@/components/YouTubeForm2/index2";

function SummaryContent() {
  return (
    <div className="min-h-screen w-full overflow-x-hidden bg-gray-900">
      <main className="w-full max-w-screen-2xl mx-auto px-2 sm:px-4">
        <YouTubeForm2 />
      </main>
    </div>
  );
}

export default function YouTubeSummarizer2() {
  return (
    <Suspense fallback={<div className="min-h-screen w-full overflow-x-hidden bg-gray-900 flex items-center justify-center"><div className="text-white">Loading...</div></div>}>
      <SummaryContent />
    </Suspense>
  );
}
