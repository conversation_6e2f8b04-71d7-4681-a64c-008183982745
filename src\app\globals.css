@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}










/* Markdown content styles */
.markdown-content {
  @apply text-foreground;
  font-family: var(--font-inter);
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  letter-spacing: -0.08px;
  line-height: 22px;
  /* Ensure consistent text color across all devices */
  color: var(--foreground) !important;
}

/* Mobile-specific fixes for markdown content */
@media (max-width: 767px) {
  .markdown-content {
    color: #ededed !important;
  }

  .markdown-content * {
    color: inherit !important;
  }
}

.markdown-content h1 {
  @apply text-2xl font-bold my-4;
  color: inherit;
}

.markdown-content h2 {
  @apply text-xl font-bold my-3;
  color: inherit;
}

.markdown-content h3 {
  @apply text-lg font-bold my-2;
  color: inherit;
}

.markdown-content h4 {
  @apply text-base font-bold my-2;
  color: inherit;
}

.markdown-content p {
  @apply my-4; /* More spacing for all paragraphs */
  white-space: pre-wrap; /* Preserve line breaks */
  color: inherit;
}

.markdown-content ul {
  @apply list-disc pl-6 my-2 space-y-4; /* Increased spacing between list items */
  color: inherit;
}

.markdown-content ol {
  @apply list-decimal pl-6 my-2 space-y-4; /* Increased spacing between list items */
  color: inherit;
}

.markdown-content li {
  @apply my-1;
  white-space: pre-wrap; /* Preserve line breaks */
  color: inherit;
}

/* Make emojis stand out */
.markdown-content span[role="img"],
.markdown-content p > span:first-child {
  @apply mr-2 text-lg;
}

.markdown-content blockquote {
  @apply border-l-4 border-gray-300 dark:border-gray-600 pl-4 italic my-2;
}

.markdown-content hr {
  @apply my-4 border-gray-300 dark:border-gray-600;
}

.markdown-content a {
  @apply text-blue-600 dark:text-blue-400 hover:underline;
}

.markdown-content code {
  @apply bg-gray-100 dark:bg-gray-700 px-1 py-0.5 rounded text-sm;
}

.markdown-content pre {
  @apply block bg-gray-100 dark:bg-gray-700 p-3 rounded-md text-sm overflow-x-auto my-2;
}

.markdown-content table {
  @apply border-collapse table-auto w-full my-4;
}

.markdown-content thead {
  @apply bg-gray-100 dark:bg-gray-700;
}

.markdown-content tr {
  @apply border-b border-gray-200 dark:border-gray-700;
}

.markdown-content th {
  @apply px-4 py-2 text-left;
}

.markdown-content td {
  @apply px-4 py-2;
}

/* Section styling for better visual separation */
.markdown-content h2 + p,
.markdown-content h3 + p,
.markdown-content h4 + p {
  @apply mt-2;
}

/* Special styling for summary sections */
.markdown-content h2 {
  @apply mt-6 mb-4 pb-2 border-b border-gray-200 dark:border-gray-700;
}

/* Specific styling for summary section headers */
.markdown-content h2:first-of-type {
  @apply mt-0;
}

/* Line clamp utilities */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.animate-bounce {
  animation: bounce 5s infinite;
}