"use client";

import { useFormContext } from "./FormContext";

export default function YouTubeUrlInput() {
  const { videoUrl, setVideoUrl, getVideoInfo, loading } = useFormContext();

  // Handle URL change
  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newUrl = e.target.value;
    setVideoUrl(newUrl);
    // If URL looks like a valid YouTube URL, try to get video info
    if (
      newUrl.includes("youtube.com/watch?v=") ||
      newUrl.includes("youtu.be/")
    ) {
      getVideoInfo(newUrl);
    }
  };

  return (
    <>
      <input
        type="text"
        value={videoUrl}
        onChange={handleUrlChange}
        required
        placeholder="https://youtu.be/i3eI6pRwU6I?si=3JNL4C3bVWyU4P76"
        className="flex-1 bg-gray-800 border border-gray-700 rounded-lg px-6 py-4 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent w-full sm:w-auto"
      />
      <button
        type="submit"
        disabled={loading}
        className="bg-gradient-to-r from-purple-500 to-pink-500 px-8 py-4 rounded-lg font-bold text-lg hover:from-purple-600 hover:to-pink-600 transition-all transform hover:scale-105 w-full sm:w-auto disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
      >
        {loading ? (
          <div className="flex items-center justify-center">
            <svg
              className="animate-spin h-5 w-5 mr-2"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              ></circle>
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
            Generating...
          </div>
        ) : (
          "Summarize It"
        )}
      </button>
    </>
  );
}
