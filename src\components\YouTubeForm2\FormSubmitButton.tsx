'use client';

import { Button } from '@/components/ui/button';
import LoadingSpinner from '../LoadingSpinner';
import { useFormContext } from './FormContext';

export default function FormSubmitButton() {
  const { loading } = useFormContext();

  return (
    <Button
      type="submit"
      className="h-full px-6 bg-blue-600 hover:bg-blue-700 text-white"
      disabled={loading}
    >
      {loading ? (
        <div className="flex items-center justify-center gap-2">
          <LoadingSpinner />
          <span>Processing...</span>
        </div>
      ) : (
        <div className="flex items-center gap-2">
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3" />
          </svg>
          <span>Generate</span>
        </div>
      )}
    </Button>
  );
}
