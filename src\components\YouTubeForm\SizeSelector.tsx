'use client';

import Select from '../ui/SelectPlain';
import { useFormContext } from './FormContext';
import { OutputSize } from '@/app/actions';

export default function SizeSelector() {
  const { size, setSize } = useFormContext();

  const sizeOptions = [
    { value: 'concise', label: 'Concise' },
    { value: 'auto', label: 'Auto' },
    { value: 'detailed', label: 'Detailed' }
  ];

  return (
    <Select
      id="size"
      value={size}
      onChange={(value) => setSize(value as OutputSize)}
      options={sizeOptions}
      title="Choose how detailed your summary should be"
    />
  );
}
